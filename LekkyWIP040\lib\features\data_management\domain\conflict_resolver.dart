// File: lib/features/data_management/domain/conflict_resolver.dart
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';

/// Conflict resolution strategies for handling duplicate entries
enum ConflictStrategy {
  /// Skip the conflicting entry
  skip,
  
  /// Replace the existing entry with the new one
  replace,
  
  /// Keep both entries
  keepBoth,
  
  /// Merge the entries (combine their data)
  merge
}

/// A class for resolving conflicts between imported and existing entries
class ConflictResolver {
  /// Logger instance
  final logger = Logger('ConflictResolver');

  /// Detect conflicts between new entries and existing entries
  /// 
  /// Returns a map of conflicting entries with their existing counterparts
  Map<MeterEntry, MeterEntry> detectConflicts(
      List<MeterEntry> newEntries, List<MeterEntry> existingEntries) {
    final conflicts = <MeterEntry, MeterEntry>{};
    
    // Create a map of existing entries by date string for faster lookup
    final existingByDate = <String, List<MeterEntry>>{};
    for (final entry in existingEntries) {
      final dateStr = _getDateString(entry.timestamp);
      existingByDate[dateStr] = existingByDate[dateStr] ?? [];
      existingByDate[dateStr]!.add(entry);
    }
    
    // Check each new entry for conflicts
    for (final newEntry in newEntries) {
      final dateStr = _getDateString(newEntry.timestamp);
      
      // If we have existing entries on this date
      if (existingByDate.containsKey(dateStr)) {
        // Check for entries of the same type
        for (final existingEntry in existingByDate[dateStr]!) {
          if (existingEntry.typeCode == newEntry.typeCode) {
            // We have a conflict
            conflicts[newEntry] = existingEntry;
            break;
          }
        }
      }
    }
    
    return conflicts;
  }

  /// Resolve conflicts using the specified strategy
  /// 
  /// Returns a list of entries after conflict resolution
  List<MeterEntry> resolveConflicts(
      Map<MeterEntry, MeterEntry> conflicts,
      List<MeterEntry> newEntries,
      List<MeterEntry> existingEntries,
      ConflictStrategy strategy) {
    // Create a copy of existing entries that we'll modify
    final result = List<MeterEntry>.from(existingEntries);
    
    // Create a set of new entries to add (we'll remove conflicting ones based on strategy)
    final entriesToAdd = newEntries.toSet();
    
    // Handle each conflict according to the strategy
    for (final conflict in conflicts.entries) {
      final newEntry = conflict.key;
      final existingEntry = conflict.value;
      
      switch (strategy) {
        case ConflictStrategy.skip:
          // Skip the new entry
          entriesToAdd.remove(newEntry);
          break;
          
        case ConflictStrategy.replace:
          // Replace the existing entry with the new one
          final index = result.indexOf(existingEntry);
          if (index >= 0) {
            result[index] = newEntry;
          }
          entriesToAdd.remove(newEntry);
          break;
          
        case ConflictStrategy.keepBoth:
          // Keep both entries (do nothing)
          // Adjust the timestamp of the new entry slightly to avoid exact duplicates
          final adjustedEntry = _adjustTimestamp(newEntry);
          entriesToAdd.remove(newEntry);
          entriesToAdd.add(adjustedEntry);
          break;
          
        case ConflictStrategy.merge:
          // Merge the entries
          final mergedEntry = _mergeEntries(existingEntry, newEntry);
          final index = result.indexOf(existingEntry);
          if (index >= 0) {
            result[index] = mergedEntry;
          }
          entriesToAdd.remove(newEntry);
          break;
      }
    }
    
    // Add the remaining non-conflicting entries
    result.addAll(entriesToAdd);
    
    // Sort by timestamp
    result.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    
    return result;
  }

  /// Get a string representation of a date (without time) for comparison
  String _getDateString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Adjust the timestamp of an entry slightly to avoid exact duplicates
  MeterEntry _adjustTimestamp(MeterEntry entry) {
    // Add one minute to the timestamp
    final adjustedTime = entry.timestamp.add(const Duration(minutes: 1));
    
    return MeterEntry(
      id: entry.id,
      reading: entry.reading,
      amountToppedUp: entry.amountToppedUp,
      date: adjustedTime,
      typeCode: entry.typeCode,
      shortAverageAfterTopUp: entry.shortAverageAfterTopUp,
      totalAverageUpToThisPoint: entry.totalAverageUpToThisPoint,
    );
  }

  /// Merge two entries
  MeterEntry _mergeEntries(MeterEntry existing, MeterEntry newEntry) {
    // For meter readings, take the lower value (more conservative)
    // For top-ups, take the sum of both
    
    if (existing.isReading && newEntry.isReading) {
      // For readings, take the lower value
      final reading = existing.reading < newEntry.reading 
          ? existing.reading 
          : newEntry.reading;
          
      return MeterEntry(
        id: existing.id,
        reading: reading,
        amountToppedUp: 0,
        date: existing.timestamp,
        typeCode: existing.typeCode,
        shortAverageAfterTopUp: null, // Will be recalculated
        totalAverageUpToThisPoint: null, // Will be recalculated
      );
    } else if (existing.isTopUp && newEntry.isTopUp) {
      // For top-ups, combine the amounts
      return MeterEntry(
        id: existing.id,
        reading: 0,
        amountToppedUp: existing.amountToppedUp + newEntry.amountToppedUp,
        date: existing.timestamp,
        typeCode: existing.typeCode,
        shortAverageAfterTopUp: null, // Will be recalculated
        totalAverageUpToThisPoint: null, // Will be recalculated
      );
    }
    
    // This shouldn't happen as we only merge entries of the same type
    logger.w('Attempted to merge entries of different types', 
        details: {'existing': existing.typeCode, 'new': newEntry.typeCode});
    return existing;
  }
}
