import '../utils/bulk_operation_context.dart';
import '../utils/logger.dart';
import '../utils/event_bus.dart';
import '../di/service_locator.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../../features/averages/domain/repositories/average_repository.dart';
import '../../features/averages/domain/repositories/per_reading_average_repository.dart';

/// Service for handling bulk data deletion operations
class DataDeletionService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final AverageRepository _averageRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;

  /// Constructor
  DataDeletionService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._averageRepository,
    this._perReadingAverageRepository,
  );

  /// Factory constructor using service locator
  factory DataDeletionService.create() {
    return DataDeletionService(
      serviceLocator<MeterReadingRepository>(),
      serviceLocator<TopUpRepository>(),
      serviceLocator<AverageRepository>(),
      serviceLocator<PerReadingAverageRepository>(),
    );
  }

  /// Get current data counts
  Future<DataCounts> getDataCounts() async {
    try {
      final meterReadingsCount =
          await _meterReadingRepository.getMeterReadingsCount();
      final topUpsCount = await _topUpRepository.getTopUpsCount();

      return DataCounts(
        meterReadings: meterReadingsCount,
        topUps: topUpsCount,
      );
    } catch (e) {
      Logger.error('Failed to get data counts: $e');
      return const DataCounts(meterReadings: 0, topUps: 0);
    }
  }

  /// Delete all data from the database
  Future<bool> deleteAllData({Function(double)? onProgress}) async {
    try {
      Logger.info('DataDeletionService: Starting bulk data deletion');

      // Start bulk operation to prevent individual events
      BulkOperationContext.startBulkOperation();

      onProgress?.call(0.1);

      // Get all entries for deletion
      final allMeterReadings =
          await _meterReadingRepository.getAllMeterReadings();
      final allTopUps = await _topUpRepository.getAllTopUps();

      onProgress?.call(0.2);

      // Delete all meter readings
      for (final reading in allMeterReadings) {
        if (reading.id != null) {
          await _meterReadingRepository.deleteMeterReading(reading.id!);
        }
      }

      onProgress?.call(0.5);

      // Delete all top-ups
      for (final topUp in allTopUps) {
        if (topUp.id != null) {
          await _topUpRepository.deleteTopUp(topUp.id!);
        }
      }

      onProgress?.call(0.7);

      // Delete all averages
      await _averageRepository.deleteAllAverages();
      await _perReadingAverageRepository.deleteAllPerReadingAverages();

      onProgress?.call(0.9);

      // End bulk operation and fire events
      BulkOperationContext.endBulkOperation();
      EventBus().fire(EventType.dataUpdated);

      onProgress?.call(1.0);

      Logger.info('DataDeletionService: Successfully deleted all data');
      return true;
    } catch (e) {
      Logger.error('DataDeletionService: Failed to delete all data: $e');

      // Ensure bulk operation is ended even on error
      if (BulkOperationContext.isBulkOperation) {
        BulkOperationContext.endBulkOperation();
      }

      return false;
    }
  }
}

/// Data counts model
class DataCounts {
  final int meterReadings;
  final int topUps;

  const DataCounts({
    required this.meterReadings,
    required this.topUps,
  });

  /// Total number of entries
  int get total => meterReadings + topUps;

  /// Check if there is any data
  bool get hasData => total > 0;
}
