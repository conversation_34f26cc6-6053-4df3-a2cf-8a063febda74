// File: lib/features/entries/domain/models/entry_state.dart

import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../presentation/controllers/entry_controller.dart';

/// Immutable state for entry management
class EntryState {
  /// Current entry type
  final EntryType entryType;

  /// Original entry type (for tracking type changes)
  final EntryType? originalEntryType;

  /// Current date and time
  final DateTime dateTime;

  /// Current value/amount
  final double value;

  /// Current notes
  final String notes;

  /// Loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Validation errors
  final Map<String, String> validationErrors;

  /// Constructor
  const EntryState({
    this.entryType = EntryType.meterReading,
    this.originalEntryType,
    required this.dateTime,
    this.value = 0.0,
    this.notes = '',
    this.isLoading = false,
    this.errorMessage,
    this.validationErrors = const {},
  });

  /// Create initial entry state
  factory EntryState.initial() {
    return EntryState(
      dateTime: DateTime.now(),
    );
  }

  /// Create entry state from meter reading
  factory EntryState.fromMeterReading(MeterReading meterReading) {
    return EntryState(
      entryType: EntryType.meterReading,
      originalEntryType: EntryType.meterReading,
      dateTime: meterReading.date,
      value: meterReading.value,
      notes: meterReading.notes ?? '',
    );
  }

  /// Create entry state from top-up
  factory EntryState.fromTopUp(TopUp topUp) {
    return EntryState(
      entryType: EntryType.topUp,
      originalEntryType: EntryType.topUp,
      dateTime: topUp.date,
      value: topUp.amount,
      notes: topUp.notes ?? '',
    );
  }

  /// Create a copy with some fields changed
  EntryState copyWith({
    EntryType? entryType,
    EntryType? originalEntryType,
    DateTime? dateTime,
    double? value,
    String? notes,
    bool? isLoading,
    String? errorMessage,
    Map<String, String>? validationErrors,
    bool clearError = false,
    bool clearValidationErrors = false,
  }) {
    return EntryState(
      entryType: entryType ?? this.entryType,
      originalEntryType: originalEntryType ?? this.originalEntryType,
      dateTime: dateTime ?? this.dateTime,
      value: value ?? this.value,
      notes: notes ?? this.notes,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: clearError ? null : (errorMessage ?? this.errorMessage),
      validationErrors: clearValidationErrors
          ? {}
          : (validationErrors ?? this.validationErrors),
    );
  }

  /// Check if the entry is valid
  bool get isValid => validationErrors.isEmpty;

  /// Check if the entry type has changed from original
  bool get hasTypeChanged =>
      originalEntryType != null && entryType != originalEntryType;

  /// Get validation error for a specific field
  String? getValidationError(String field) => validationErrors[field];

  /// Check if a specific field has validation error
  bool hasValidationError(String field) => validationErrors.containsKey(field);

  @override
  String toString() {
    return 'EntryState(entryType: $entryType, dateTime: $dateTime, value: $value, notes: $notes, isLoading: $isLoading, errorMessage: $errorMessage, validationErrors: $validationErrors)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is EntryState &&
        other.entryType == entryType &&
        other.originalEntryType == originalEntryType &&
        other.dateTime == dateTime &&
        other.value == value &&
        other.notes == notes &&
        other.isLoading == isLoading &&
        other.errorMessage == errorMessage &&
        _mapEquals(other.validationErrors, validationErrors);
  }

  @override
  int get hashCode {
    return entryType.hashCode ^
        originalEntryType.hashCode ^
        dateTime.hashCode ^
        value.hashCode ^
        notes.hashCode ^
        isLoading.hashCode ^
        errorMessage.hashCode ^
        validationErrors.hashCode;
  }

  /// Helper method to compare maps
  bool _mapEquals(Map<String, String> map1, Map<String, String> map2) {
    if (map1.length != map2.length) return false;
    for (final key in map1.keys) {
      if (map1[key] != map2[key]) return false;
    }
    return true;
  }
}
