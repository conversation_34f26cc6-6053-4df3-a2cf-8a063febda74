// File: lib/features/entries/presentation/dialogs/riverpod_add_entry_dialog.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../controllers/entry_controller.dart';
import '../providers/entry_provider.dart';
import '../widgets/entry_type_selector.dart';

/// A Riverpod dialog for adding a new entry (meter reading or top-up)
class RiverpodAddEntryDialog extends ConsumerStatefulWidget {
  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when an entry is added
  final VoidCallback onEntryAdded;

  /// Constructor
  const RiverpodAddEntryDialog({
    super.key,
    this.currencySymbol = '₦',
    required this.onEntryAdded,
  });

  @override
  ConsumerState<RiverpodAddEntryDialog> createState() =>
      _RiverpodAddEntryDialogState();
}

class _RiverpodAddEntryDialogState
    extends ConsumerState<RiverpodAddEntryDialog> {
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Reset the entry provider to initial state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(entryProvider.notifier).reset();
      _updateTextControllers();
    });
  }

  @override
  void dispose() {
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    // Debug: Print the calculated width
    debugPrint(
        'RiverpodAddEntryDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  /// Update text controllers when the state values change
  void _updateTextControllers() {
    final entryState = ref.read(entryProvider);
    if (_valueController.text != entryState.value.toString()) {
      _valueController.text = entryState.value.toString();
    }
    if (_notesController.text != entryState.notes) {
      _notesController.text = entryState.notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    final entryState = ref.watch(entryProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final dialogWidth = _getDialogWidth(context);
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28, // Reduced from 32 to increase height by 4% more
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildDialogHeader(context),
              const SizedBox(height: 24),
              EntryTypeSelector(
                selectedType: entryState.entryType,
                onTypeChanged: (type) {
                  ref.read(entryProvider.notifier).setEntryType(type);
                  _updateTextControllers();
                },
              ),
              const SizedBox(height: 24),
              _buildDateTimePicker(context, entryState),
              const SizedBox(height: 24),
              _buildValueInput(context, entryState),
              const SizedBox(height: 24),
              _buildNotesInput(context, entryState),
              const SizedBox(height: 24),
              _buildButtonBar(context, entryState),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.add,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Add Entry',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the date and time picker
  Widget _buildDateTimePicker(BuildContext context, entryState) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date and Time',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateTime(),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: entryState.hasValidationError('dateTime')
                    ? theme.colorScheme.error
                    : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  DateFormatter.formatDateTimeForEntry(entryState.dateTime),
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_drop_down,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ),
        if (entryState.hasValidationError('dateTime'))
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 8),
            child: Text(
              entryState.getValidationError('dateTime')!,
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Build the value input field
  Widget _buildValueInput(BuildContext context, entryState) {
    final theme = Theme.of(context);
    final String label = entryState.entryType == EntryType.meterReading
        ? 'Meter Reading'
        : 'Top-up Amount';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        CurrencyInputField(
          value: entryState.value,
          onChanged: (value) =>
              ref.read(entryProvider.notifier).setValue(value ?? 0.0),
          currencySymbol: widget.currencySymbol,
          labelText: '',
          errorText: entryState.getValidationError('value'),
          borderRadius: BorderRadius.circular(8),
        ),
      ],
    );
  }

  /// Build the notes input field
  Widget _buildNotesInput(BuildContext context, entryState) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) =>
              ref.read(entryProvider.notifier).setNotes(value),
        ),
      ],
    );
  }

  /// Build the button bar
  Widget _buildButtonBar(BuildContext context, entryState) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Save',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            isLoading: entryState.isLoading,
            onPressed: entryState.isLoading ? null : () => _saveEntry(),
          ),
        ),
      ],
    );
  }

  /// Show date and time picker
  Future<void> _selectDateTime() async {
    final entryState = ref.read(entryProvider);
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: entryState.dateTime,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (!mounted) return;

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(entryState.dateTime),
      );

      if (!mounted) return;

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        ref.read(entryProvider.notifier).setDateTime(newDateTime);
      }
    }
  }

  /// Save the entry
  Future<void> _saveEntry() async {
    final success = await ref.read(entryProvider.notifier).saveEntry();

    // Check mounted after async operation before using context
    if (!mounted) return;

    if (success) {
      widget.onEntryAdded();
      Navigator.of(context).pop();
    } else {
      final entryState = ref.read(entryProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(entryState.errorMessage ?? 'Failed to save entry'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
