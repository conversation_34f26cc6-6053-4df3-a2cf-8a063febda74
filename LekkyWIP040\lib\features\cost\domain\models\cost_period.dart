// File: lib/features/cost/domain/models/cost_period.dart

/// Enum representing different cost calculation periods
enum CostPeriod {
  /// Past day
  pastDay('Day', 1, true),

  /// Past week
  pastWeek('Week', 7, true),

  /// Past month
  pastMonth('Month', 30, true),

  /// Past year
  pastYear('Year', 365, true),

  /// Future day
  futureDay('Day', 1, false),

  /// Future week
  futureWeek('Week', 7, false),

  /// Future month
  futureMonth('Month', 30, false),

  /// Future year
  futureYear('Year', 365, false),

  /// Custom period
  custom('Custom', 0, false);

  /// Constructor
  const CostPeriod(this.name, this.days, this.isPast);

  /// Display name of the period
  final String name;

  /// Number of days in the period
  final int days;

  /// Whether this is a past period
  final bool isPast;

  /// Get periods by mode (past or future)
  static List<CostPeriod> getPeriodsByMode(bool isPastMode) {
    if (isPastMode) {
      return [pastDay, pastWeek, pastMonth, pastYear, custom];
    } else {
      return [futureDay, futureWeek, futureMonth, futureYear, custom];
    }
  }

  /// Get period by name and mode
  static CostPeriod getPeriodByNameAndMode(String periodName, bool isPastMode) {
    switch (periodName) {
      case 'Day':
        return isPastMode ? pastDay : futureDay;
      case 'Week':
        return isPastMode ? pastWeek : futureWeek;
      case 'Month':
        return isPastMode ? pastMonth : futureMonth;
      case 'Year':
        return isPastMode ? pastYear : futureYear;
      case 'Custom':
        return custom;
      default:
        return isPastMode ? pastDay : futureDay;
    }
  }

  /// Get the display name for the period
  String get displayName => name;

  /// Check if this is a custom period
  bool get isCustom => this == custom;

  /// Get the corresponding future period for a past period
  CostPeriod get futurePeriod {
    switch (this) {
      case pastDay:
        return futureDay;
      case pastWeek:
        return futureWeek;
      case pastMonth:
        return futureMonth;
      case pastYear:
        return futureYear;
      default:
        return this;
    }
  }

  /// Get the corresponding past period for a future period
  CostPeriod get pastPeriod {
    switch (this) {
      case futureDay:
        return pastDay;
      case futureWeek:
        return pastWeek;
      case futureMonth:
        return pastMonth;
      case futureYear:
        return pastYear;
      default:
        return this;
    }
  }

  /// Calculate actual calendar days for the period from current date
  int calculateActualDays({DateTime? fromDate, DateTime? toDate}) {
    final now = DateTime.now();

    switch (this) {
      case pastDay:
      case futureDay:
        return 1;
      case pastWeek:
      case futureWeek:
        return 7;
      case pastMonth:
        // Previous calendar month days
        final prevMonthEnd = DateTime(now.year, now.month, 0);
        return prevMonthEnd.day;
      case futureMonth:
        // Days from now to same day next month (rounded up)
        final nextMonth = DateTime(now.year, now.month + 1, now.day);
        return (nextMonth.difference(now).inHours / 24).ceil();
      case pastYear:
        // Previous calendar year days
        final prevYear = now.year - 1;
        return _isLeapYear(prevYear) ? 366 : 365;
      case futureYear:
        // Days from now to same day next year (rounded up)
        final nextYear = DateTime(now.year + 1, now.month, now.day);
        return (nextYear.difference(now).inHours / 24).ceil();
      case custom:
        if (fromDate != null && toDate != null) {
          // Custom range: count partial days as full days
          return (toDate.difference(fromDate).inHours / 24).ceil();
        }
        return 0;
    }
  }

  /// Check if a year is a leap year
  static bool _isLeapYear(int year) {
    return (year % 4 == 0) && (year % 100 != 0 || year % 400 == 0);
  }
}
