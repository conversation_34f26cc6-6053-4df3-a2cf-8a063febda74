// File: integration_test/flows/add_entry_flow_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:lekky/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Add Entry Flow Integration Tests', () {
    testWidgets('should complete add meter reading flow', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Look for add entry dialog or screen
      final addEntryDialog = find.text('Add Entry');
      if (addEntryDialog.evaluate().isNotEmpty) {
        // Fill the meter reading form
        final valueField = find.byType(TextFormField).first;
        await tester.enterText(valueField, '100.0');
        await tester.pumpAndSettle();

        // Select meter reading type if needed
        final meterReadingOption = find.text('Meter Reading');
        if (meterReadingOption.evaluate().isNotEmpty) {
          await tester.tap(meterReadingOption);
          await tester.pumpAndSettle();
        }

        // Save the entry
        final saveButton = find.text('Save');
        if (saveButton.evaluate().isNotEmpty) {
          await tester.tap(saveButton);
          await tester.pumpAndSettle();
        }

        // Assert - Verify the entry was added
        expect(find.text('100.0'), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('should complete add top-up flow', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Look for add entry dialog or screen
      final addEntryDialog = find.text('Add Entry');
      if (addEntryDialog.evaluate().isNotEmpty) {
        // Select top-up type
        final topUpOption = find.text('Top-up');
        if (topUpOption.evaluate().isNotEmpty) {
          await tester.tap(topUpOption);
          await tester.pumpAndSettle();
        }

        // Fill the top-up amount
        final valueField = find.byType(TextFormField).first;
        await tester.enterText(valueField, '20.0');
        await tester.pumpAndSettle();

        // Save the entry
        final saveButton = find.text('Save');
        if (saveButton.evaluate().isNotEmpty) {
          await tester.tap(saveButton);
          await tester.pumpAndSettle();
        }

        // Assert - Verify the entry was added
        expect(find.text('20.0'), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Try to save without entering data
      final saveButton = find.text('Save');
      if (saveButton.evaluate().isNotEmpty) {
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // Assert - Should show validation error
        expect(find.text('Please enter a value'), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('should handle negative values validation', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Enter negative value
      final valueField = find.byType(TextFormField).first;
      await tester.enterText(valueField, '-10.0');
      await tester.pumpAndSettle();

      // Try to save
      final saveButton = find.text('Save');
      if (saveButton.evaluate().isNotEmpty) {
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // Assert - Should show validation error
        expect(find.textContaining('positive'), findsAtLeastNWidgets(1));
      }
    });

    testWidgets('should cancel add entry flow', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Enter some data
      final valueField = find.byType(TextFormField).first;
      await tester.enterText(valueField, '50.0');
      await tester.pumpAndSettle();

      // Cancel the operation
      final cancelButton = find.text('Cancel');
      if (cancelButton.evaluate().isNotEmpty) {
        await tester.tap(cancelButton);
        await tester.pumpAndSettle();

        // Assert - Should return to previous screen without saving
        expect(find.text('Add Entry'), findsNothing);
      }
    });

    testWidgets('should handle date selection', (WidgetTester tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle();

      // Wait for splash screen to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Act - Navigate to add entry screen
      final addButton = find.byIcon(Icons.add);
      if (addButton.evaluate().isNotEmpty) {
        await tester.tap(addButton);
        await tester.pumpAndSettle();
      }

      // Look for date picker button
      final dateButton = find.byIcon(Icons.calendar_today);
      if (dateButton.evaluate().isNotEmpty) {
        await tester.tap(dateButton);
        await tester.pumpAndSettle();

        // Select a date (if date picker opens)
        final okButton = find.text('OK');
        if (okButton.evaluate().isNotEmpty) {
          await tester.tap(okButton);
          await tester.pumpAndSettle();
        }
      }

      // Fill value and save
      final valueField = find.byType(TextFormField).first;
      await tester.enterText(valueField, '75.0');
      await tester.pumpAndSettle();

      final saveButton = find.text('Save');
      if (saveButton.evaluate().isNotEmpty) {
        await tester.tap(saveButton);
        await tester.pumpAndSettle();

        // Assert - Entry should be saved with selected date
        expect(find.text('75.0'), findsAtLeastNWidgets(1));
      }
    });
  });
}
