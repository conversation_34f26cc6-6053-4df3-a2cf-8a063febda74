import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/services/unified_alert_manager.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../../home/<USER>/providers/dashboard_provider.dart';
import '../../../home/<USER>/models/dashboard_state.dart';
import '../../domain/models/notification.dart';

/// Widget that listens to dashboard state changes and triggers alerts
class ReactiveAlertListener extends ConsumerStatefulWidget {
  final Widget child;

  const ReactiveAlertListener({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<ReactiveAlertListener> createState() =>
      _ReactiveAlertListenerState();
}

class _ReactiveAlertListenerState extends ConsumerState<ReactiveAlertListener>
    with WidgetsBindingObserver {
  bool _hasRequestedPermissions = false;
  bool _isCheckingAlerts = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Request permissions and check alerts after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestPermissionsAndCheckAlerts();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      Logger.info('ReactiveAlertListener: App resumed, checking alerts');
      _checkAlertsIfPermitted();
      _checkForPendingInAppAlerts();
      _checkForBackgroundAlertRequests();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to dashboard state changes and trigger alerts
    ref.listen<AsyncValue<DashboardState>>(dashboardProvider, (previous, next) {
      next.whenData((dashboardState) {
        if (previous?.value != null && previous?.value != dashboardState) {
          Logger.info(
              'ReactiveAlertListener: Dashboard state changed, checking alerts');
          _checkAlertsIfPermitted();
        }
      });
    });

    return widget.child;
  }

  /// Request notification permissions and check alerts
  Future<void> _requestPermissionsAndCheckAlerts() async {
    if (_hasRequestedPermissions) return;

    try {
      Logger.info('ReactiveAlertListener: Requesting notification permissions');

      final permissionManager = NotificationPermissionManager();
      final hasPermission = await permissionManager.requestPermission(context);

      if (hasPermission) {
        Logger.info(
            'ReactiveAlertListener: Permissions granted, checking alerts');
        await _checkAlerts();
      } else {
        Logger.warning(
            'ReactiveAlertListener: Permissions denied, alerts will not work');
      }

      _hasRequestedPermissions = true;
    } catch (e) {
      Logger.error('ReactiveAlertListener: Error requesting permissions: $e');
      _hasRequestedPermissions = true; // Don't keep trying
    }
  }

  /// Check alerts if permissions are granted
  Future<void> _checkAlertsIfPermitted() async {
    if (!_hasRequestedPermissions) {
      await _requestPermissionsAndCheckAlerts();
      return;
    }

    try {
      final permissionManager = NotificationPermissionManager();
      final hasPermission = await permissionManager.hasPermission();

      if (hasPermission) {
        await _checkAlerts();
      } else {
        Logger.info(
            'ReactiveAlertListener: No notification permissions, skipping alert check');
      }
    } catch (e) {
      Logger.error('ReactiveAlertListener: Error checking permissions: $e');
    }
  }

  /// Check and fire alerts
  Future<void> _checkAlerts() async {
    if (_isCheckingAlerts) {
      Logger.info(
          'ReactiveAlertListener: Alert check already in progress, skipping');
      return;
    }

    _isCheckingAlerts = true;

    try {
      final alertManager = UnifiedAlertManager();
      await alertManager.checkAndFireAlerts();
    } catch (e) {
      Logger.error('ReactiveAlertListener: Error checking alerts: $e');
    } finally {
      _isCheckingAlerts = false;
    }
  }

  /// Check for pending in-app alerts and show them
  Future<void> _checkForPendingInAppAlerts() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final title = prefs.getString('pending_in_app_alert_title');
      final message = prefs.getString('pending_in_app_alert_message');
      final typeString = prefs.getString('pending_in_app_alert_type');
      final timestampString = prefs.getString('pending_in_app_alert_timestamp');

      if (title != null &&
          message != null &&
          typeString != null &&
          timestampString != null) {
        try {
          final timestamp = DateTime.parse(timestampString);
          final now = DateTime.now();

          // Only show alerts from the last 24 hours
          if (now.difference(timestamp).inHours < 24) {
            Logger.info('ReactiveAlertListener: Showing pending in-app alert');

            // Show the alert dialog
            if (mounted) {
              _showInAppAlertDialog(title, message);
            }
          }

          // Clear the pending alert
          await prefs.remove('pending_in_app_alert_title');
          await prefs.remove('pending_in_app_alert_message');
          await prefs.remove('pending_in_app_alert_type');
          await prefs.remove('pending_in_app_alert_timestamp');
        } catch (e) {
          Logger.error(
              'ReactiveAlertListener: Error parsing pending alert: $e');
          // Clear invalid data
          await prefs.remove('pending_in_app_alert_title');
          await prefs.remove('pending_in_app_alert_message');
          await prefs.remove('pending_in_app_alert_type');
          await prefs.remove('pending_in_app_alert_timestamp');
        }
      }
    } catch (e) {
      Logger.error(
          'ReactiveAlertListener: Error checking pending in-app alerts: $e');
    }
  }

  /// Show in-app alert dialog
  void _showInAppAlertDialog(String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.warning,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to dashboard or relevant screen
                // This could be enhanced to navigate to specific screens based on alert type
              },
              child: const Text('View Details'),
            ),
          ],
        );
      },
    );
  }

  /// Check for background alert requests and process them
  Future<void> _checkForBackgroundAlertRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final alertCheckRequested =
          prefs.getBool('background_alert_check_requested') ?? false;
      final alertCheckTimeString =
          prefs.getString('background_alert_check_time');

      if (alertCheckRequested && alertCheckTimeString != null) {
        try {
          final alertCheckTime = DateTime.parse(alertCheckTimeString);
          final now = DateTime.now();

          // Only process requests from the last 6 hours
          if (now.difference(alertCheckTime).inHours < 6) {
            Logger.info(
                'ReactiveAlertListener: Processing background alert request');
            await _checkAlerts();
          }

          // Clear the request flag
          await prefs.remove('background_alert_check_requested');
          await prefs.remove('background_alert_check_time');
        } catch (e) {
          Logger.error(
              'ReactiveAlertListener: Error parsing background alert request: $e');
          // Clear invalid data
          await prefs.remove('background_alert_check_requested');
          await prefs.remove('background_alert_check_time');
        }
      }
    } catch (e) {
      Logger.error(
          'ReactiveAlertListener: Error checking background alert requests: $e');
    }
  }
}
