import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../theme/app_colors.dart';

/// Individual date picker widget with label and constraints
class CustomDatePickerWidget extends StatelessWidget {
  /// Label for the date picker (e.g., 'From', 'To')
  final String label;

  /// Currently selected date
  final DateTime? selectedDate;

  /// Callback when date is selected
  final Function(DateTime) onDateSelected;

  /// First selectable date
  final DateTime firstDate;

  /// Last selectable date
  final DateTime lastDate;

  /// Default date to show when picker opens
  final DateTime defaultDate;

  /// Theme context for styling
  final String themeContext;

  /// Constructor
  const CustomDatePickerWidget({
    super.key,
    required this.label,
    required this.selectedDate,
    required this.onDateSelected,
    required this.firstDate,
    required this.lastDate,
    required this.defaultDate,
    this.themeContext = 'history',
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getTextColor(isDark),
              ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 36,
          child: ElevatedButton(
            onPressed: () => _showDatePicker(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: _getBackgroundColor(isDark),
              foregroundColor: _getForegroundColor(isDark),
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: _getForegroundColor(isDark),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedDate != null
                        ? dateFormat.format(selectedDate!)
                        : 'Select date',
                    style: TextStyle(
                      fontSize: 12,
                      color: _getForegroundColor(isDark),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Show date picker dialog
  Future<void> _showDatePicker(BuildContext context) async {
    if (!context.mounted) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? defaultDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: _getPrimaryColor(context),
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && context.mounted) {
      onDateSelected(picked);
    }
  }

  /// Get text color based on theme context
  Color _getTextColor(bool isDark) {
    switch (themeContext) {
      case 'cost':
        return AppColors.getAppBarTextColor('cost', isDark).withOpacity(0.9);
      case 'history':
      default:
        return isDark ? Colors.white.withOpacity(0.9) : Colors.black.withOpacity(0.9);
    }
  }

  /// Get background color for button
  Color _getBackgroundColor(bool isDark) {
    return isDark ? Colors.black : Colors.white;
  }

  /// Get foreground color for button
  Color _getForegroundColor(bool isDark) {
    return isDark ? Colors.white : Colors.black;
  }

  /// Get primary color for date picker theme
  Color _getPrimaryColor(BuildContext context) {
    switch (themeContext) {
      case 'cost':
        return Theme.of(context).colorScheme.primary;
      case 'history':
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
