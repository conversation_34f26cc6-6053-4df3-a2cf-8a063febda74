import 'package:shared_preferences/shared_preferences.dart';
import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import '../../features/home/<USER>/models/dashboard_state.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';

/// Service for predicting when alerts will trigger and pre-scheduling system notifications
class AlertPredictionService {
  static final AlertPredictionService _instance =
      AlertPredictionService._internal();

  factory AlertPredictionService() => _instance;
  AlertPredictionService._internal();

  static const String _scheduledAlertIdsKey = 'scheduled_alert_ids';

  /// Predict and schedule future alerts based on current usage patterns
  Future<void> predictAndScheduleAlerts() async {
    try {
      final settings = await _loadAlertSettings();

      if (!settings['notificationsEnabled']) {
        Logger.info('Notifications disabled, skipping alert prediction');
        return;
      }

      // Cancel existing scheduled alerts
      await _cancelScheduledAlerts();

      final scheduledIds = <int>[];
      final notificationService =
          await serviceLocator.getAsync<NotificationService>();

      // Note: AlertCoordinationService was removed - this service is deprecated
      Logger.info(
          'AlertPredictionService: Service temporarily disabled due to architecture changes');
      return;
    } catch (e) {
      Logger.error('Error predicting and scheduling alerts: $e');
    }
  }

  /// Predict when low balance alert will trigger
  Future<Map<String, dynamic>?> _predictLowBalanceAlert(
    DashboardState dashboardState,
  ) async {
    try {
      final daysToZero = dashboardState.calculateDaysToMeterZero();

      // Only predict if condition will be met in the future (> 24hrs but < 7 days)
      if (daysToZero == null || daysToZero <= 1.0 || daysToZero > 7.0) {
        return null;
      }

      // Calculate when the alert should trigger (24 hours before zero)
      final alertTriggerTime = DateTime.now().add(
        Duration(hours: ((daysToZero - 1.0) * 24).round()),
      );

      // Check if we already sent this alert today
      if (await _wasNotificationSentToday(NotificationType.lowBalance)) {
        return null;
      }

      final notification = AppNotification(
        title: 'Low Balance Alert',
        message: 'Your meter balance is running low. Consider topping up soon.',
        timestamp: alertTriggerTime,
        type: NotificationType.lowBalance,
      );

      return {
        'notification': notification,
        'scheduledTime': alertTriggerTime,
      };
    } catch (e) {
      Logger.error('Error predicting low balance alert: $e');
      return null;
    }
  }

  /// Predict when alert threshold alert will trigger
  Future<Map<String, dynamic>?> _predictAlertThresholdAlert(
    DashboardState dashboardState,
    double alertThreshold,
    int daysInAdvance,
  ) async {
    try {
      final daysToThreshold = dashboardState.calculateDaysToAlertThreshold(
        alertThreshold,
        daysInAdvance,
      );

      // Only predict if condition will be met in the future (> 24hrs but < 7 days)
      if (daysToThreshold == null ||
          daysToThreshold <= 1.0 ||
          daysToThreshold > 7.0) {
        return null;
      }

      // Calculate when the alert should trigger (24 hours before threshold)
      final alertTriggerTime = DateTime.now().add(
        Duration(hours: ((daysToThreshold - 1.0) * 24).round()),
      );

      // Check if we already sent this alert today
      if (await _wasNotificationSentToday(NotificationType.timeToTopUp)) {
        return null;
      }

      final notification = AppNotification(
        title: 'Time to Top-Up',
        message:
            'Your meter will reach the alert threshold soon. Time to top up.',
        timestamp: alertTriggerTime,
        type: NotificationType.timeToTopUp,
      );

      return {
        'notification': notification,
        'scheduledTime': alertTriggerTime,
      };
    } catch (e) {
      Logger.error('Error predicting threshold alert: $e');
      return null;
    }
  }

  /// Cancel all scheduled alerts
  Future<void> _cancelScheduledAlerts() async {
    try {
      final scheduledIds = await _getScheduledAlertIds();

      if (scheduledIds.isNotEmpty) {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        for (final id in scheduledIds) {
          try {
            await notificationService.cancelNotification(id);
          } catch (e) {
            Logger.error('Failed to cancel scheduled alert $id: $e');
          }
        }

        await _clearScheduledAlertIds();
        Logger.info('Cancelled ${scheduledIds.length} scheduled alerts');
      }
    } catch (e) {
      Logger.error('Error cancelling scheduled alerts: $e');
    }
  }

  /// Load alert settings from preferences
  Future<Map<String, dynamic>> _loadAlertSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'notificationsEnabled': (prefs
                    .getBool(PreferenceKeys.lowBalanceAlertsEnabled) ??
                false) ||
            (prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false) ||
            (prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false),
        'lowBalanceEnabled':
            prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false,
        'timeToTopUpEnabled':
            prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false,
        'alertThreshold': prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0,
        'daysInAdvance': prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5,
      };
    } catch (e) {
      Logger.error('Error loading alert settings: $e');
      return {
        'notificationsEnabled': false,
        'lowBalanceEnabled': false,
        'timeToTopUpEnabled': false,
        'alertThreshold': 5.0,
        'daysInAdvance': 5,
      };
    }
  }

  /// Check if notification was sent today for deduplication
  Future<bool> _wasNotificationSentToday(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final lastDateString = prefs.getString(key);

      if (lastDateString == null) return false;

      final lastDate = DateTime.parse(lastDateString);
      final today = DateTime.now();

      return lastDate.year == today.year &&
          lastDate.month == today.month &&
          lastDate.day == today.day;
    } catch (e) {
      Logger.error('Error checking notification deduplication: $e');
      return false;
    }
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError('Unsupported notification type: $type');
    }
  }

  /// Store scheduled alert IDs
  Future<void> _storeScheduledAlertIds(List<int> ids) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = ids.map((id) => id.toString()).join(',');
      await prefs.setString(_scheduledAlertIdsKey, idsString);
    } catch (e) {
      Logger.error('Error storing scheduled alert IDs: $e');
    }
  }

  /// Get scheduled alert IDs
  Future<List<int>> _getScheduledAlertIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final idsString = prefs.getString(_scheduledAlertIdsKey);

      if (idsString == null || idsString.isEmpty) return [];

      return idsString
          .split(',')
          .where((s) => s.isNotEmpty)
          .map((s) => int.tryParse(s))
          .where((id) => id != null)
          .cast<int>()
          .toList();
    } catch (e) {
      Logger.error('Error getting scheduled alert IDs: $e');
      return [];
    }
  }

  /// Clear scheduled alert IDs
  Future<void> _clearScheduledAlertIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_scheduledAlertIdsKey);
    } catch (e) {
      Logger.error('Error clearing scheduled alert IDs: $e');
    }
  }
}
