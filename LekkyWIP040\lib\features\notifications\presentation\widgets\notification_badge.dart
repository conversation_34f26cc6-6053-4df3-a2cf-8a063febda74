import 'package:flutter/material.dart';

/// A badge that displays the number of unread notifications
///
/// Features:
/// - Displays a bell icon (filled when there are notifications, outlined when there are none)
/// - Shows a red badge with the unread count when there are unread notifications
/// - Dynamically sizes the badge based on the number of digits
/// - Shows "99+" when there are more than 99 unread notifications
class NotificationBadge extends StatelessWidget {
  /// The number of unread notifications
  final int count;

  /// Callback when the badge is tapped
  final VoidCallback onTap;

  /// Constructor
  const NotificationBadge({
    super.key,
    required this.count,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Format the count text (99+ for counts > 99)
    final String displayCount = count > 99 ? '99+' : count.toString();

    // Determine badge size based on count
    final double minWidth =
        displayCount.length > 1 ? (displayCount.length > 2 ? 24 : 20) : 16;

    // Adjust font size based on count
    final double fontSize = displayCount.length > 2 ? 8 : 10;

    return Stack(
      children: [
        IconButton(
          icon: Icon(
            count > 0 ? Icons.notifications : Icons.notifications_outlined,
            color: Colors.white,
            size: 28, // Larger icon size to match screenshot
          ),
          onPressed: onTap,
          tooltip: 'Notifications',
          padding: EdgeInsets.zero, // Remove padding for better positioning
          constraints:
              const BoxConstraints(), // Remove constraints for better positioning
        ),
        if (count > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                    color: Colors.white,
                    width: 1), // White border for better contrast
              ),
              constraints: BoxConstraints(
                minWidth: minWidth,
                minHeight: 16,
              ),
              child: Text(
                displayCount,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: fontSize,
                  fontWeight:
                      FontWeight.bold, // Bold text for better visibility
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
