import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../features/setup/presentation/widgets/region_settings_card.dart';

/// Region settings screen
class RegionScreen extends ConsumerWidget {
  /// Constructor
  const RegionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Region Settings'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Region settings card
            RegionSettingsCard(),
          ],
        ),
      ),
    );
  }
}
