^D:\000.WORKSPACE\LEKKY\BUILD\WINDOWS\X64\CMAKEFILES\00BA921351404DC654AB9F3E403895FD\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/000.Workspace/Lekky/windows -BD:/000.Workspace/Lekky/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/000.Workspace/Lekky/build/windows/x64/lekky.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
