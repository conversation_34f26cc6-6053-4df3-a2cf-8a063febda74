import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../domain/models/alert_state_manager_state.dart';

part 'alert_state_manager_provider.g.dart';

/// Consolidated alert state management using Riverpod
@riverpod
class AlertStateManager extends _$AlertStateManager {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<AlertStateManagerState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    return await _loadInitialState();
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.dataUpdated) {
        Logger.info(
            'AlertStateManager: Received dataUpdated event, checking for resets');
        _handleDataUpdate();
      }
    });
  }

  /// Handle data update events (top-ups, meter readings)
  Future<void> _handleDataUpdate() async {
    try {
      // Check if low balance alert should be reset due to top-up
      await _checkAndResetLowBalanceAlert();

      // Check if time to top-up alert should be reset due to top-up
      await _checkAndResetTimeToTopUpAlert();

      // Refresh state after resets
      ref.invalidateSelf();

      Logger.info(
          'AlertStateManager: Completed data update checks and refreshed state');
    } catch (e) {
      Logger.error('AlertStateManager: Error handling data update: $e');
    }
  }

  /// Check and reset low balance alert if balance improved
  Future<void> _checkAndResetLowBalanceAlert() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastAlertDate =
          prefs.getString(PreferenceKeys.lastLowBalanceNotificationDate);

      if (lastAlertDate != null) {
        // Reset the alert state to allow new alerts
        await prefs.remove(PreferenceKeys.lastLowBalanceNotificationDate);
        Logger.info(
            'AlertStateManager: Reset low balance alert state due to data update');
      }
    } catch (e) {
      Logger.error('AlertStateManager: Error resetting low balance alert: $e');
    }
  }

  /// Check and reset time to top-up alert if balance improved
  Future<void> _checkAndResetTimeToTopUpAlert() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastAlertDate =
          prefs.getString(PreferenceKeys.lastTimeToTopUpNotificationDate);

      if (lastAlertDate != null) {
        // Reset the alert state to allow new alerts
        await prefs.remove(PreferenceKeys.lastTimeToTopUpNotificationDate);
        Logger.info(
            'AlertStateManager: Reset time to top-up alert state due to data update');
      }
    } catch (e) {
      Logger.error(
          'AlertStateManager: Error resetting time to top-up alert: $e');
    }
  }

  /// Check if meter reading was taken after 3 AM today
  Future<bool> hasReadingAfter3AMToday() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      final now = DateTime.now();
      final today3AM = DateTime(now.year, now.month, now.day, 3, 0, 0);

      // Check if any reading exists after 3 AM today
      final hasRecentReading =
          allReadings.any((reading) => reading.date.isAfter(today3AM));

      Logger.info(
          'AlertStateManager: Reading after 3 AM today: $hasRecentReading');
      return hasRecentReading;
    } catch (e) {
      Logger.error('AlertStateManager: Error checking recent readings: $e');
      return false; // Default to false if error
    }
  }

  /// Manually reset all time-based alert states
  Future<void> resetAllTimeBasedAlerts() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Reset low balance alert state
      await prefs.remove(PreferenceKeys.lastLowBalanceNotificationDate);

      // Reset time to top-up alert state
      await prefs.remove(PreferenceKeys.lastTimeToTopUpNotificationDate);

      // Note: Invalid record alerts are not reset as they're not time-based

      Logger.info(
          'AlertStateManager: Manually reset all time-based alert states');

      // Refresh state
      ref.invalidateSelf();
    } catch (e) {
      Logger.error('AlertStateManager: Error resetting time-based alerts: $e');
      rethrow;
    }
  }

  /// Load initial alert state
  Future<AlertStateManagerState> _loadInitialState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lastLowBalanceAlert =
          prefs.getString(PreferenceKeys.lastLowBalanceNotificationDate);
      final lastTimeToTopUpAlert =
          prefs.getString(PreferenceKeys.lastTimeToTopUpNotificationDate);

      return AlertStateManagerState(
        lastLowBalanceAlertDate: lastLowBalanceAlert != null
            ? DateTime.tryParse(lastLowBalanceAlert)
            : null,
        lastTimeToTopUpAlertDate: lastTimeToTopUpAlert != null
            ? DateTime.tryParse(lastTimeToTopUpAlert)
            : null,
        isLoading: false,
      );
    } catch (e) {
      Logger.error('AlertStateManager: Error loading initial state: $e');
      return AlertStateManagerState.initial();
    }
  }
}
