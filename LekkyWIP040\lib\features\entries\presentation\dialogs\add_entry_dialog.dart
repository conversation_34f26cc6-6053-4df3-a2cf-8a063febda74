import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/utils/date_formatter.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../controllers/entry_controller.dart';
import '../widgets/entry_type_selector.dart';

/// A dialog for adding a new entry (meter reading or top-up)
class AddEntryDialog extends StatefulWidget {
  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when an entry is added
  final VoidCallback onEntryAdded;

  /// Constructor
  const AddEntryDialog({
    super.key,
    this.currencySymbol = '₦',
    required this.onEntryAdded,
  });

  @override
  State<AddEntryDialog> createState() => _AddEntryDialogState();
}

class _AddEntryDialogState extends State<AddEntryDialog> {
  late EntryController _controller;
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = serviceLocator<EntryController>();
    _valueController.text = _controller.value.toString();
    _notesController.text = _controller.notes;

    // Listen for changes to update the text controllers
    _controller.addListener(_updateTextControllers);
  }

  @override
  void dispose() {
    _controller.removeListener(_updateTextControllers);
    _controller.dispose();
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    // Debug: Print the calculated width
    debugPrint(
        'AddEntryDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  /// Update text controllers when the controller values change
  void _updateTextControllers() {
    if (_valueController.text != _controller.value.toString()) {
      _valueController.text = _controller.value.toString();
    }
    if (_notesController.text != _controller.notes) {
      _notesController.text = _controller.notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Consumer<EntryController>(
        builder: (context, controller, _) {
          final screenWidth = MediaQuery.of(context).size.width;
          final dialogWidth = _getDialogWidth(context);
          final horizontalPadding = (screenWidth - dialogWidth) / 2;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 24,
            insetPadding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: 28, // Reduced from 32 to increase height by 4% more
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildDialogHeader(context),
                    const SizedBox(height: 24),
                    EntryTypeSelector(
                      selectedType: controller.entryType,
                      onTypeChanged: controller.setEntryType,
                    ),
                    const SizedBox(height: 24),
                    _buildDateTimePicker(context, controller),
                    const SizedBox(height: 24),
                    _buildValueInput(context, controller),
                    const SizedBox(height: 24),
                    _buildNotesInput(context, controller),
                    const SizedBox(height: 24),
                    _buildButtonBar(context, controller),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build the dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.add,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Add Entry',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the date and time picker
  Widget _buildDateTimePicker(
      BuildContext context, EntryController controller) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date and Time',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateTime(controller),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: controller.validationErrors.containsKey('dateTime')
                    ? theme.colorScheme.error
                    : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  DateFormatter.formatDateTimeForEntry(controller.dateTime),
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_drop_down,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ),
        if (controller.validationErrors.containsKey('dateTime'))
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 8),
            child: Text(
              controller.validationErrors['dateTime']!,
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Build the value input field
  Widget _buildValueInput(BuildContext context, EntryController controller) {
    final theme = Theme.of(context);
    final String label = controller.entryType == EntryType.meterReading
        ? 'Meter Reading'
        : 'Top-up Amount';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        CurrencyInputField(
          value: controller.value,
          onChanged: (value) => controller.setValue(value ?? 0.0),
          currencySymbol: widget.currencySymbol,
          labelText: '',
          errorText: controller.validationErrors['value'],
          borderRadius: BorderRadius.circular(8),
        ),
      ],
    );
  }

  /// Build the notes input field
  Widget _buildNotesInput(BuildContext context, EntryController controller) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: controller.setNotes,
        ),
      ],
    );
  }

  /// Build the button bar
  Widget _buildButtonBar(BuildContext context, EntryController controller) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Save',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            isLoading: controller.isLoading,
            onPressed:
                controller.isLoading ? null : () => _saveEntry(controller),
          ),
        ),
      ],
    );
  }

  /// Show date and time picker
  Future<void> _selectDateTime(EntryController controller) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: controller.dateTime,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (!mounted) return;

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(controller.dateTime),
      );

      if (!mounted) return;

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        controller.setDateTime(newDateTime);
      }
    }
  }

  /// Save the entry
  Future<void> _saveEntry(EntryController controller) async {
    final success = await controller.saveEntry();

    // Check mounted after async operation before using context
    if (!mounted) return;

    if (success) {
      widget.onEntryAdded();
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(controller.errorMessage ?? 'Failed to save entry'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
