// File: lib/core/utils/database_inspector.dart
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/database_constants.dart';
import 'logger.dart';

/// Utility for inspecting database contents during debugging
class DatabaseInspector {
  static final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// Get detailed database information including entry counts and sample data
  static Future<Map<String, dynamic>> getDetailedDatabaseInfo() async {
    try {
      final db = await _databaseHelper.database;
      
      // Get table counts
      final meterReadingsCount = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable}'
      )) ?? 0;
      
      final topUpsCount = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable}'
      )) ?? 0;
      
      // Get recent entries for verification
      final recentMeterReadings = await db.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date DESC',
        limit: 5,
      );
      
      final recentTopUps = await db.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date DESC', 
        limit: 5,
      );
      
      // Get validation status counts
      final validMeterReadings = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable} WHERE status = 0'
      )) ?? 0;
      
      final invalidMeterReadings = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable} WHERE status = 1'
      )) ?? 0;
      
      final ignoredMeterReadings = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable} WHERE status = 2'
      )) ?? 0;
      
      final validTopUps = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable} WHERE status = 0'
      )) ?? 0;
      
      final invalidTopUps = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable} WHERE status = 1'
      )) ?? 0;
      
      final ignoredTopUps = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable} WHERE status = 2'
      )) ?? 0;
      
      return {
        'meter_readings_count': meterReadingsCount,
        'top_ups_count': topUpsCount,
        'total_entries': meterReadingsCount + topUpsCount,
        'recent_meter_readings': recentMeterReadings,
        'recent_top_ups': recentTopUps,
        'validation_status': {
          'meter_readings': {
            'valid': validMeterReadings,
            'invalid': invalidMeterReadings,
            'ignored': ignoredMeterReadings,
          },
          'top_ups': {
            'valid': validTopUps,
            'invalid': invalidTopUps,
            'ignored': ignoredTopUps,
          },
        },
      };
    } catch (e) {
      Logger.error('Failed to get detailed database info: $e');
      return {
        'error': e.toString(),
        'meter_readings_count': 0,
        'top_ups_count': 0,
        'total_entries': 0,
      };
    }
  }

  /// Log database contents for debugging
  static Future<void> logDatabaseContents() async {
    try {
      final info = await getDetailedDatabaseInfo();
      
      Logger.info('=== DATABASE INSPECTION ===');
      Logger.info('Meter Readings: ${info['meter_readings_count']}');
      Logger.info('Top-ups: ${info['top_ups_count']}');
      Logger.info('Total Entries: ${info['total_entries']}');
      
      if (info['validation_status'] != null) {
        final validation = info['validation_status'];
        Logger.info('Validation Status:');
        Logger.info('  Meter Readings - Valid: ${validation['meter_readings']['valid']}, Invalid: ${validation['meter_readings']['invalid']}, Ignored: ${validation['meter_readings']['ignored']}');
        Logger.info('  Top-ups - Valid: ${validation['top_ups']['valid']}, Invalid: ${validation['top_ups']['invalid']}, Ignored: ${validation['top_ups']['ignored']}');
      }
      
      if (info['recent_meter_readings'] != null && (info['recent_meter_readings'] as List).isNotEmpty) {
        Logger.info('Recent Meter Readings:');
        for (final reading in info['recent_meter_readings'] as List) {
          Logger.info('  ID: ${reading['id']}, Value: ${reading['value']}, Date: ${reading['date']}, Status: ${reading['status']}');
        }
      }
      
      if (info['recent_top_ups'] != null && (info['recent_top_ups'] as List).isNotEmpty) {
        Logger.info('Recent Top-ups:');
        for (final topUp in info['recent_top_ups'] as List) {
          Logger.info('  ID: ${topUp['id']}, Amount: ${topUp['amount']}, Date: ${topUp['date']}, Status: ${topUp['status']}');
        }
      }
      
      Logger.info('=== END DATABASE INSPECTION ===');
    } catch (e) {
      Logger.error('Failed to log database contents: $e');
    }
  }

  /// Check if data was inserted in the last N minutes
  static Future<Map<String, int>> getRecentInsertCounts({int minutesAgo = 5}) async {
    try {
      final db = await _databaseHelper.database;
      final cutoffTime = DateTime.now().subtract(Duration(minutes: minutesAgo)).toIso8601String();
      
      final recentMeterReadings = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable} WHERE created_at > ?',
        [cutoffTime]
      )) ?? 0;
      
      final recentTopUps = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable} WHERE created_at > ?',
        [cutoffTime]
      )) ?? 0;
      
      return {
        'meter_readings': recentMeterReadings,
        'top_ups': recentTopUps,
        'total': recentMeterReadings + recentTopUps,
      };
    } catch (e) {
      Logger.error('Failed to get recent insert counts: $e');
      return {
        'meter_readings': 0,
        'top_ups': 0,
        'total': 0,
      };
    }
  }
}
