import 'dart:developer' as developer;

/// Logger utility for consistent logging throughout the app
class Logger {
  /// Tag for the logger
  final String _tag;

  /// Constructor
  Logger([this._tag = 'APP']);

  /// Log an info message (instance method)
  void i(String message, {dynamic details}) {
    Logger.info(
        '[$_tag] $message${details != null ? ' - Details: $details' : ''}');
  }

  /// Log a warning message (instance method)
  void w(String message, {dynamic details}) {
    Logger.warning(
        '[$_tag] $message${details != null ? ' - Details: $details' : ''}');
  }

  /// Log an error message (instance method)
  void e(String message, {dynamic details, StackTrace? stackTrace}) {
    Logger.error(
      '[$_tag] $message${details != null ? ' - Details: $details' : ''}',
      details,
      stackTrace,
    );
  }

  /// Log a debug message (instance method)
  void d(String message, {dynamic details}) {
    Logger.debug(
        '[$_tag] $message${details != null ? ' - Details: $details' : ''}');
  }

  /// Log an info message (static method)
  static void info(String message) {
    developer.log(
      message,
      name: 'INFO',
      time: DateTime.now(),
    );
  }

  /// Log a warning message (static method)
  static void warning(String message) {
    developer.log(
      message,
      name: 'WARNING',
      time: DateTime.now(),
    );
  }

  /// Log an error message (static method)
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    developer.log(
      message,
      name: 'ERROR',
      time: DateTime.now(),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// Log a debug message (only in debug mode) (static method)
  static void debug(String message) {
    developer.log(
      message,
      name: 'DEBUG',
      time: DateTime.now(),
    );
  }
}
