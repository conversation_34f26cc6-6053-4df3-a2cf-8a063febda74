import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/date_format.dart';

/// Service for managing shared preferences
class PreferenceService extends ChangeNotifier {
  /// Date format
  DateFormat _dateFormat = DateFormat.ddMmYyyy;

  /// Whether to show time with date
  bool _showTimeWithDate = true;

  /// Alert threshold
  double _alertThreshold = 5.0;

  /// Days in advance
  int _daysInAdvance = 5;

  /// Language
  String _language = 'English';

  /// Currency
  String _currency = 'GBP';

  /// Currency symbol
  String _currencySymbol = '£';

  /// Whether this is the first launch of the app
  bool _isFirstLaunch = true;

  /// Get date format
  DateFormat get dateFormat => _dateFormat;

  /// Get whether to show time with date
  bool get showTimeWithDate => _showTimeWithDate;

  /// Get alert threshold
  double get alertThreshold => _alertThreshold;

  /// Get days in advance
  int get daysInAdvance => _daysInAdvance;

  /// Get language
  String get language => _language;

  /// Get currency
  String get currency => _currency;

  /// Get currency symbol
  String get currencySymbol => _currencySymbol;

  /// Get whether this is the first launch of the app
  bool get isFirstLaunch => _isFirstLaunch;

  /// Constructor
  PreferenceService() {
    _loadPreferences();
  }

  /// Load preferences from SharedPreferences
  Future<void> _loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final dateFormatStr = prefs.getString(PreferenceKeys.dateFormat);
      if (dateFormatStr != null) {
        _dateFormat = DateFormat.fromString(dateFormatStr);
      }

      _showTimeWithDate =
          prefs.getBool(PreferenceKeys.showTimeWithDate) ?? true;
      _alertThreshold = prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0;
      _daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5;
      _language = prefs.getString(PreferenceKeys.language) ?? 'English';
      _currency = prefs.getString(PreferenceKeys.currency) ?? 'GBP';
      _currencySymbol = prefs.getString(PreferenceKeys.currencySymbol) ?? '£';

      // Check if this is the first launch
      _isFirstLaunch = prefs.getBool(PreferenceKeys.firstLaunch) ?? true;

      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error loading preferences: $e');
    }
  }

  /// Set date format
  Future<void> setDateFormat(DateFormat format) async {
    if (_dateFormat == format) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.dateFormat, format.formatString);

      _dateFormat = format;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting date format: $e');
    }
  }

  /// Set whether to show time with date
  Future<void> setShowTimeWithDate(bool show) async {
    if (_showTimeWithDate == show) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.showTimeWithDate, show);

      _showTimeWithDate = show;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting show time with date: $e');
    }
  }

  /// Set alert threshold
  Future<void> setAlertThreshold(double threshold) async {
    if (_alertThreshold == threshold) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(PreferenceKeys.alertThreshold, threshold);

      _alertThreshold = threshold;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting alert threshold: $e');
    }
  }

  /// Set days in advance
  Future<void> setDaysInAdvance(int days) async {
    if (_daysInAdvance == days) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(PreferenceKeys.daysInAdvance, days);

      _daysInAdvance = days;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting days in advance: $e');
    }
  }

  /// Set language
  Future<void> setLanguage(String language) async {
    if (_language == language) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, language);

      _language = language;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting language: $e');
    }
  }

  /// Set currency
  Future<void> setCurrency(String currency, String symbol) async {
    if (_currency == currency && _currencySymbol == symbol) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.currency, currency);
      await prefs.setString(PreferenceKeys.currencySymbol, symbol);

      _currency = currency;
      _currencySymbol = symbol;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting currency: $e');
    }
  }

  /// Set first launch status
  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    if (_isFirstLaunch == isFirstLaunch) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.firstLaunch, isFirstLaunch);

      _isFirstLaunch = isFirstLaunch;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting first launch status: $e');
    }
  }
}
