import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';
import '../widgets/lekky_button.dart';

/// Manages notification permissions across Android and iOS platforms
class NotificationPermissionManager {
  static final NotificationPermissionManager _instance =
      NotificationPermissionManager._internal();

  factory NotificationPermissionManager() => _instance;
  NotificationPermissionManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Check if notification permissions are granted
  Future<bool> hasPermission() async {
    try {
      if (Platform.isAndroid) {
        return await _checkAndroidPermission();
      } else if (Platform.isIOS) {
        return await _checkIOSPermission();
      }
      return false;
    } catch (e) {
      Logger.error('Error checking notification permission: $e');
      return false;
    }
  }

  /// Request notification permissions with platform-specific handling
  Future<bool> requestPermission(BuildContext context) async {
    try {
      // Show explanation dialog first
      final shouldRequest = await _showPermissionExplanationDialog(context);
      if (!shouldRequest) return false;

      if (Platform.isAndroid) {
        return await _requestAndroidPermission();
      } else if (Platform.isIOS) {
        return await _requestIOSPermission();
      }
      return false;
    } catch (e) {
      Logger.error('Error requesting notification permission: $e');
      return false;
    }
  }

  /// Check Android notification permission (API 33+)
  Future<bool> _checkAndroidPermission() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+), check POST_NOTIFICATIONS permission
      final status = await Permission.notification.status;
      return status.isGranted;
    }
    return true; // Assume granted for older Android versions
  }

  /// Check iOS notification permission
  Future<bool> _checkIOSPermission() async {
    final settings = await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    return settings ?? false;
  }

  /// Request Android notification permission
  Future<bool> _requestAndroidPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.request();
      return status.isGranted;
    }
    return true;
  }

  /// Request iOS notification permission
  Future<bool> _requestIOSPermission() async {
    final granted = await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
    return granted ?? false;
  }

  /// Show permission explanation dialog
  Future<bool> _showPermissionExplanationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return _buildPermissionDialog(context);
          },
        ) ??
        false;
  }

  /// Build permission dialog with Lekky styling
  Widget _buildPermissionDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical:
            screenHeight * 0.1, // 10% of screen height for top/bottom padding
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8, // Maximum 80% of screen height
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed header
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              child: _buildDialogHeader(context),
            ),
            // Scrollable content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPermissionText(context),
                    const SizedBox(height: 20),
                    _buildFeaturesList(context),
                  ],
                ),
              ),
            ),
            // Fixed button bar
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
              child: _buildButtonBar(context),
            ),
          ],
        ),
      ),
    );
  }

  /// Show settings dialog when permission is permanently denied
  Future<void> showSettingsDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return _buildSettingsDialog(context);
      },
    );
  }

  /// Build settings dialog with Lekky styling
  Widget _buildSettingsDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: screenHeight * 0.1,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed header
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              child: _buildSettingsDialogHeader(context),
            ),
            // Scrollable content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: _buildSettingsText(context),
              ),
            ),
            // Fixed button bar
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
              child: _buildSettingsButtonBar(context),
            ),
          ],
        ),
      ),
    );
  }

  /// Build dialog header with notification icon
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.notifications_active,
          color: theme.colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Enable Notifications',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.of(context).pop(false),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build permission explanation text
  Widget _buildPermissionText(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'Lekky needs notification permission to help you manage your electricity usage effectively.',
      style: TextStyle(
        fontSize: 16,
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
    );
  }

  /// Build features list showing what notifications will be used for
  Widget _buildFeaturesList(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.notifications,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Notifications will alert you about:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildFeatureRow(
              context, Icons.battery_alert, 'Low balance warnings'),
          const SizedBox(height: 8),
          _buildFeatureRow(context, Icons.schedule, 'Time to top-up reminders'),
          const SizedBox(height: 8),
          _buildFeatureRow(
              context, Icons.error_outline, 'Invalid record alerts'),
        ],
      ),
    );
  }

  /// Build individual feature row
  Widget _buildFeatureRow(BuildContext context, IconData icon, String text) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 18,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  /// Build button bar with Cancel and Allow buttons
  Widget _buildButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(false),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Allow',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ),
      ],
    );
  }

  /// Build settings dialog header with warning icon
  Widget _buildSettingsDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        const Icon(
          Icons.settings,
          color: Colors.orange,
          size: 28,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Permission Required',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        IconButton(
          icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build settings explanation text
  Widget _buildSettingsText(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'Notification permission is required for alerts. Please enable notifications in your device settings to receive important alerts about your electricity usage.',
      style: TextStyle(
        fontSize: 16,
        color: theme.colorScheme.onSurface,
        height: 1.4,
      ),
    );
  }

  /// Build settings button bar with Cancel and Open Settings buttons
  Widget _buildSettingsButtonBar(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Open Settings',
            type: LekkyButtonType.primary,
            size: LekkyButtonSize.compact,
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
          ),
        ),
      ],
    );
  }
}
