// File: lib/features/cost/presentation/widgets/cost_chart_card.dart
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/widgets/app_card.dart';
import '../models/chart_data.dart';

/// Widget that displays cost data in a chart format
class CostChartCard extends StatelessWidget {
  final String title;
  final List<ChartData> chartData;
  final Color? lineColor;

  const CostChartCard({
    super.key,
    required this.title,
    required this.chartData,
    this.lineColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: chartData.isEmpty
                  ? Center(
                      child: Text(
                        'No data available',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.6),
                            ),
                      ),
                    )
                  : LineChart(
                      LineChartData(
                        gridData: FlGridData(show: true),
                        titlesData: FlTitlesData(show: true),
                        borderData: FlBorderData(show: true),
                        lineBarsData: [
                          LineChartBarData(
                            spots: _convertToFlSpots(chartData),
                            isCurved: true,
                            color: lineColor ??
                                Theme.of(context).colorScheme.primary,
                            barWidth: 3,
                            dotData: FlDotData(show: false),
                            belowBarData: BarAreaData(
                              show: true,
                              color: (lineColor ??
                                      Theme.of(context).colorScheme.primary)
                                  .withOpacity(0.1),
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// Convert ChartData to FlSpot for chart display
  List<FlSpot> _convertToFlSpots(List<ChartData> data) {
    return data.asMap().entries.map((entry) {
      final index = entry.key;
      final chartData = entry.value;
      // Use index as x-axis and cost as y-axis
      return FlSpot(index.toDouble(), chartData.cost);
    }).toList();
  }
}
