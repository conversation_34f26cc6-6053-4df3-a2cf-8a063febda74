import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import '../../../../core/di/service_locator.dart';
import '../../../../core/providers/date_formatter_provider.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../controllers/entry_controller.dart';
import '../widgets/entry_type_selector.dart';
import 'delete_confirmation_dialog.dart';

/// A dialog for editing an existing entry (meter reading or top-up)
class EditEntryDialog extends ConsumerStatefulWidget {
  /// The meter reading to edit (null if editing a top-up)
  final MeterReading? meterReading;

  /// The top-up to edit (null if editing a meter reading)
  final TopUp? topUp;

  /// Currency symbol to use
  final String currencySymbol;

  /// Callback when an entry is updated
  final VoidCallback onEntryUpdated;

  /// Callback when an entry is deleted
  final VoidCallback onEntryDeleted;

  /// Constructor
  const EditEntryDialog({
    super.key,
    this.meterReading,
    this.topUp,
    this.currencySymbol = '₦',
    required this.onEntryUpdated,
    required this.onEntryDeleted,
  });

  @override
  ConsumerState<EditEntryDialog> createState() => _EditEntryDialogState();
}

class _EditEntryDialogState extends ConsumerState<EditEntryDialog> {
  late EntryController _controller;
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = serviceLocator<EntryController>();

    // Initialize with the existing entry
    if (widget.meterReading != null) {
      _controller.initWithMeterReading(widget.meterReading!);
    } else if (widget.topUp != null) {
      _controller.initWithTopUp(widget.topUp!);
    }

    _valueController.text = _controller.value.toString();
    _notesController.text = _controller.notes;

    // Listen for changes to update the text controllers
    _controller.addListener(_updateTextControllers);
  }

  @override
  void dispose() {
    _controller.removeListener(_updateTextControllers);
    _controller.dispose();
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Calculate responsive dialog width
  double _getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final calculatedWidth = screenWidth < 600
        ? screenWidth * 0.95 // Small and medium screens
        : 500.0; // Fixed width for larger screens

    // Debug: Print the calculated width
    debugPrint(
        'EditEntryDialog: screenWidth=$screenWidth, calculatedWidth=$calculatedWidth');
    return calculatedWidth;
  }

  /// Update text controllers when the controller values change
  void _updateTextControllers() {
    if (_valueController.text != _controller.value.toString()) {
      _valueController.text = _controller.value.toString();
    }
    if (_notesController.text != _controller.notes) {
      _notesController.text = _controller.notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    return provider.ChangeNotifierProvider.value(
      value: _controller,
      child: provider.Consumer<EntryController>(
        builder: (context, controller, _) {
          final screenWidth = MediaQuery.of(context).size.width;
          final dialogWidth = _getDialogWidth(context);
          final horizontalPadding = (screenWidth - dialogWidth) / 2;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 24,
            insetPadding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: 28, // Same height as Add Entry dialog
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildDialogHeader(context),
                    const SizedBox(height: 24),
                    EntryTypeSelector(
                      selectedType: controller.entryType,
                      onTypeChanged: (newType) {
                        controller.setEntryType(newType);
                        // Update text controllers when type changes
                        _updateTextControllers();
                      },
                      enabled: true, // Allow changing entry type when editing
                    ),
                    const SizedBox(height: 24),
                    _buildDateTimePicker(context, controller),
                    const SizedBox(height: 24),
                    _buildValueInput(context, controller),
                    const SizedBox(height: 24),
                    _buildNotesInput(context, controller),
                    const SizedBox(height: 24),
                    _buildButtonBar(context, controller),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build the dialog header
  Widget _buildDialogHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          Icons.edit,
          color: theme.colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          'Edit Entry',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Close',
        ),
      ],
    );
  }

  /// Build the date and time picker
  Widget _buildDateTimePicker(
      BuildContext context, EntryController controller) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date and Time',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateTime(controller),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(
                color: controller.validationErrors.containsKey('dateTime')
                    ? theme.colorScheme.error
                    : theme.colorScheme.outline,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 18,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
                const SizedBox(width: 8),
                Text(
                  ref
                      .watch(dateFormatterProvider)
                      .formatDateForHistory(controller.dateTime),
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_drop_down,
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ),
        if (controller.validationErrors.containsKey('dateTime'))
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 8),
            child: Text(
              controller.validationErrors['dateTime']!,
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  /// Build the value input field
  Widget _buildValueInput(BuildContext context, EntryController controller) {
    final theme = Theme.of(context);
    final String label = controller.entryType == EntryType.meterReading
        ? 'Meter Reading'
        : 'Top-up Amount';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        CurrencyInputField(
          value: controller.value,
          onChanged: (value) => controller.setValue(value ?? 0.0),
          currencySymbol: widget.currencySymbol,
          labelText: '',
          errorText: controller.validationErrors['value'],
          borderRadius: BorderRadius.circular(8),
        ),
      ],
    );
  }

  /// Build the notes input field
  Widget _buildNotesInput(BuildContext context, EntryController controller) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes here...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: controller.setNotes,
        ),
      ],
    );
  }

  /// Build the button bar
  Widget _buildButtonBar(BuildContext context, EntryController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: LekkyButton(
            text: 'Delete',
            type: LekkyButtonType.destructive,
            size: LekkyButtonSize.compact,
            onPressed: controller.isLoading
                ? null
                : () => _showDeleteConfirmation(controller),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Cancel',
            type: LekkyButtonType.secondary,
            size: LekkyButtonSize.compact,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: LekkyButton(
            text: 'Edit Entry',
            type: LekkyButtonType.special,
            size: LekkyButtonSize.compact,
            isLoading: controller.isLoading,
            onPressed:
                controller.isLoading ? null : () => _updateEntry(controller),
          ),
        ),
      ],
    );
  }

  /// Show date and time picker
  Future<void> _selectDateTime(EntryController controller) async {
    if (!mounted) return;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: controller.dateTime,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (!mounted) return;

    if (pickedDate != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(controller.dateTime),
      );

      if (!mounted) return;

      if (pickedTime != null) {
        final newDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );

        controller.setDateTime(newDateTime);
      }
    }
  }

  /// Update the entry
  Future<void> _updateEntry(EntryController controller) async {
    bool success;

    // Check if entry type has changed and handle conversion
    if (controller.hasTypeChanged) {
      if (widget.meterReading != null &&
          controller.entryType == EntryType.topUp) {
        // Convert meter reading to top-up
        success = await controller
            .convertMeterReadingToTopUp(widget.meterReading!.id!);
      } else if (widget.topUp != null &&
          controller.entryType == EntryType.meterReading) {
        // Convert top-up to meter reading
        success =
            await controller.convertTopUpToMeterReading(widget.topUp!.id!);
      } else {
        success = false;
      }
    } else {
      // Standard update without type conversion
      if (widget.meterReading != null) {
        success = await controller.updateMeterReading(widget.meterReading!.id!);
      } else if (widget.topUp != null) {
        success = await controller.updateTopUp(widget.topUp!.id!);
      } else {
        success = false;
      }
    }

    if (!mounted) return;

    if (success) {
      widget.onEntryUpdated();
      Navigator.of(context).pop();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(controller.errorMessage ?? 'Failed to update entry'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmation(EntryController controller) async {
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteConfirmationDialog(
        entryType: controller.entryType,
        value: controller.value,
        date: controller.dateTime,
        currencySymbol: widget.currencySymbol,
      ),
    );

    if (!mounted) return;

    if (confirmed == true) {
      bool success;

      if (widget.meterReading != null) {
        success = await controller.deleteMeterReading(widget.meterReading!.id!);
      } else if (widget.topUp != null) {
        success = await controller.deleteTopUp(widget.topUp!.id!);
      } else {
        success = false;
      }

      if (!mounted) return;

      if (success) {
        widget.onEntryDeleted();
        Navigator.of(context).pop();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(controller.errorMessage ?? 'Failed to delete entry'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
