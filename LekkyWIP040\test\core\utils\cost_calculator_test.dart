// File: test/core/utils/cost_calculator_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/core/models/meter_entry.dart';
import 'package:lekky/core/utils/cost_calculator.dart';

void main() {
  group('CostCalculator', () {
    test('calculateCost returns 0 for empty entries', () {
      final result = CostCalculator.calculateCost(
        [],
        DateTime(2023, 1, 1),
        DateTime(2023, 1, 31),
      );
      expect(result, 0.0);
    });

    test('calculateCost returns 0 for invalid date range', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
        ),
      ];

      // End date before start date
      final result = CostCalculator.calculateCost(
        entries,
        DateTime(2023, 1, 31),
        DateTime(2023, 1, 1),
      );
      expect(result, 0.0);
    });

    test('calculateCost handles future periods correctly', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 2.0,
          totalAverageUpToThisPoint: 2.0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 11),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 2.0,
          totalAverageUpToThisPoint: 2.0,
        ),
      ];

      // Future period (after the last meter reading)
      final result = CostCalculator.calculateCost(
        entries,
        DateTime(2023, 1, 15),
        DateTime(2023, 1, 25),
      );

      // Should use total average (2.0) * 11 days = 22.0
      expect(result, 22.0);
    });

    test(
        'calculateCost calculates cost correctly for a period with meter readings',
        () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 0.0,
          totalAverageUpToThisPoint: 2.0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 6),
          reading: 0.0,
          amountToppedUp: 20.0,
          typeCode: 1,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 11),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 4.0,
          totalAverageUpToThisPoint: 2.0,
        ),
        MeterEntry(
          id: 4,
          date: DateTime(2023, 1, 21),
          reading: 60.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 2.0,
          totalAverageUpToThisPoint: 2.0,
        ),
      ];

      // Period that spans multiple meter readings
      final result = CostCalculator.calculateCost(
        entries,
        DateTime(2023, 1, 5),
        DateTime(2023, 1, 15),
      );

      // Expected calculation:
      // - From Jan 5 to Jan 11: 6 days * 4.0 = 24.0
      // - From Jan 11 to Jan 15: 4 days * 2.0 = 8.0
      // Total: 32.0
      expect(result, closeTo(32.0, 0.1));
    });

    test('calculateCost handles periods with top-ups correctly', () {
      final entries = [
        MeterEntry(
          id: 1,
          date: DateTime(2023, 1, 1),
          reading: 100.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 0.0,
          totalAverageUpToThisPoint: 2.0,
        ),
        MeterEntry(
          id: 2,
          date: DateTime(2023, 1, 6),
          reading: 0.0,
          amountToppedUp: 20.0,
          typeCode: 1,
        ),
        MeterEntry(
          id: 3,
          date: DateTime(2023, 1, 11),
          reading: 80.0,
          amountToppedUp: 0.0,
          typeCode: 0,
          shortAverageAfterTopUp: 4.0,
          totalAverageUpToThisPoint: 2.0,
        ),
      ];

      // Period that includes a top-up
      final result = CostCalculator.calculateCost(
        entries,
        DateTime(2023, 1, 1),
        DateTime(2023, 1, 11),
      );

      // Expected calculation:
      // - From Jan 1 to Jan 11: 10 days * 4.0 = 40.0
      expect(result, closeTo(40.0, 0.1));
    });
  });
}
