import 'package:flutter/material.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../validation/domain/services/validation_trigger_service.dart';
import '../../../../core/di/service_locator.dart';

/// Entry type enum
enum EntryType {
  /// Meter reading
  meterReading,

  /// Top-up
  topUp,
}

/// Controller for managing entries (meter readings and top-ups)
class EntryController extends ChangeNotifier {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  /// Current entry type
  EntryType _entryType = EntryType.meterReading;

  /// Original entry type (for tracking type changes)
  EntryType? _originalEntryType;

  /// Current date and time
  DateTime _dateTime = DateTime.now();

  /// Current value/amount
  double _value = 0.0;

  /// Current notes
  String _notes = '';

  /// Loading state
  bool _isLoading = false;

  /// Error message
  String? _errorMessage;

  /// Validation errors
  final Map<String, String> _validationErrors = {};

  /// Constructor
  EntryController({
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
  })  : _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository;

  /// Get the current entry type
  EntryType get entryType => _entryType;

  /// Get the current date and time
  DateTime get dateTime => _dateTime;

  /// Get the current value/amount
  double get value => _value;

  /// Get the current notes
  String get notes => _notes;

  /// Get the loading state
  bool get isLoading => _isLoading;

  /// Get the error message
  String? get errorMessage => _errorMessage;

  /// Get validation errors
  Map<String, String> get validationErrors => _validationErrors;

  /// Set the entry type
  void setEntryType(EntryType type) {
    _entryType = type;
    notifyListeners();
  }

  /// Set the date and time
  void setDateTime(DateTime dateTime) {
    _dateTime = dateTime;
    validateDateTime();
    notifyListeners();
  }

  /// Set the value/amount
  void setValue(double value) {
    _value = value;
    validateValue();
    notifyListeners();
  }

  /// Set the notes
  void setNotes(String notes) {
    _notes = notes;
    notifyListeners();
  }

  /// Reset the controller to default values
  void reset() {
    _entryType = EntryType.meterReading;
    _originalEntryType = null;
    _dateTime = DateTime.now();
    _value = 0.0;
    _notes = '';
    _validationErrors.clear();
    _errorMessage = null;
    notifyListeners();
  }

  /// Initialize the controller with an existing meter reading
  void initWithMeterReading(MeterReading meterReading) {
    _entryType = EntryType.meterReading;
    _originalEntryType = EntryType.meterReading;
    _dateTime = meterReading.date;
    _value = meterReading.value;
    _notes = meterReading.notes ?? '';
    _validationErrors.clear();
    _errorMessage = null;
    notifyListeners();
  }

  /// Initialize the controller with an existing top-up
  void initWithTopUp(TopUp topUp) {
    _entryType = EntryType.topUp;
    _originalEntryType = EntryType.topUp;
    _dateTime = topUp.date;
    _value = topUp.amount;
    _notes = topUp.notes ?? '';
    _validationErrors.clear();
    _errorMessage = null;
    notifyListeners();
  }

  /// Validate the current entry
  bool validate() {
    _validationErrors.clear();

    validateDateTime();
    validateValue();

    return _validationErrors.isEmpty;
  }

  /// Validate the date and time
  void validateDateTime() {
    // Check if date is in the future
    if (_dateTime.isAfter(DateTime.now())) {
      _validationErrors['dateTime'] = 'Date cannot be in the future';
    } else {
      _validationErrors.remove('dateTime');
    }
  }

  /// Validate the value/amount
  void validateValue() {
    // Check if value is positive
    if (_value < 0) {
      _validationErrors['value'] = _entryType == EntryType.meterReading
          ? 'Meter Reading must be a positive number'
          : 'Top-up amount must be a positive number';
    } else {
      _validationErrors.remove('value');
    }
  }

  /// Save the current entry
  Future<bool> saveEntry() async {
    if (!validate()) {
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      if (_entryType == EntryType.meterReading) {
        await _saveMeterReading();
      } else {
        await _saveTopUp();
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to save entry: ${e.toString()}';
      Logger.error('EntryController saveEntry error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Save a meter reading
  Future<void> _saveMeterReading() async {
    // Create a new meter reading
    final meterReading = MeterReading(
      value: _value,
      date: _dateTime,
      status: EntryStatus.valid, // Will be validated by the repository
      notes: _notes.isNotEmpty ? _notes : null,
    );

    // Add to repository
    await _meterReadingRepository.addMeterReading(meterReading);
  }

  /// Save a top-up
  Future<void> _saveTopUp() async {
    // Create a new top-up
    final topUp = TopUp(
      amount: _value,
      date: _dateTime,
      notes: _notes.isNotEmpty ? _notes : null,
    );

    // Add to repository
    await _topUpRepository.addTopUp(topUp);
  }

  /// Update an existing meter reading
  Future<bool> updateMeterReading(int id) async {
    if (!validate()) {
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create a meter reading with the updated values
      final meterReading = MeterReading(
        id: id,
        value: _value,
        date: _dateTime,
        status: EntryStatus.valid, // Will be validated by the repository
        notes: _notes.isNotEmpty ? _notes : null,
      );

      // Update in repository
      await _meterReadingRepository.updateMeterReading(meterReading);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update meter reading: ${e.toString()}';
      Logger.error('EntryController updateMeterReading error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Update an existing top-up
  Future<bool> updateTopUp(int id) async {
    if (!validate()) {
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create a top-up with the updated values
      final topUp = TopUp(
        id: id,
        amount: _value,
        date: _dateTime,
        notes: _notes.isNotEmpty ? _notes : null,
      );

      // Update in repository
      await _topUpRepository.updateTopUp(topUp);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to update top-up: ${e.toString()}';
      Logger.error('EntryController updateTopUp error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Check if entry type has changed from original
  bool get hasTypeChanged =>
      _originalEntryType != null && _entryType != _originalEntryType;

  /// Convert a meter reading to a top-up
  Future<bool> convertMeterReadingToTopUp(int meterReadingId) async {
    if (!validate()) {
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create new top-up with current values
      final topUp = TopUp(
        amount: _value,
        date: _dateTime,
        notes: _notes.isNotEmpty ? _notes : null,
      );

      // Add top-up first, then delete meter reading (safer order)
      // Note: deleteMeterReading will automatically clean up per-reading average
      final addedTopUpId = await _topUpRepository.addTopUp(topUp);
      await _meterReadingRepository.deleteMeterReading(meterReadingId);

      // Trigger validation for the conversion
      _triggerConversionValidationAsync(addedTopUpId, _dateTime);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to convert entry: ${e.toString()}';
      Logger.error('EntryController convertMeterReadingToTopUp error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Convert a top-up to a meter reading
  Future<bool> convertTopUpToMeterReading(int topUpId) async {
    if (!validate()) {
      return false;
    }

    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Create new meter reading with current values
      final meterReading = MeterReading(
        value: _value,
        date: _dateTime,
        status: EntryStatus.valid,
        notes: _notes.isNotEmpty ? _notes : null,
      );

      // Add meter reading first, then delete top-up (safer order)
      final addedReadingId =
          await _meterReadingRepository.addMeterReading(meterReading);
      await _topUpRepository.deleteTopUp(topUpId);

      // Trigger validation for the conversion
      _triggerConversionValidationAsync(addedReadingId, _dateTime);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to convert entry: ${e.toString()}';
      Logger.error('EntryController convertTopUpToMeterReading error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Delete a meter reading
  Future<bool> deleteMeterReading(int id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Delete from repository
      await _meterReadingRepository.deleteMeterReading(id);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to delete meter reading: ${e.toString()}';
      Logger.error('EntryController deleteMeterReading error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Delete a top-up
  Future<bool> deleteTopUp(int id) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Delete from repository
      await _topUpRepository.deleteTopUp(id);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to delete top-up: ${e.toString()}';
      Logger.error('EntryController deleteTopUp error: $e');
      notifyListeners();
      return false;
    }
  }

  /// Trigger validation asynchronously after entry conversion
  void _triggerConversionValidationAsync(int entryId, DateTime entryDate) {
    try {
      final validationService = serviceLocator<ValidationTriggerService>();
      // Fire and forget - don't await to avoid blocking
      // Validate subsequent readings that might be affected by the conversion
      validationService
          .validateAfterEntryConversion(entryDate)
          .catchError((error) {
        Logger.error('Failed to trigger validation after conversion: $error');
      });
    } catch (e) {
      Logger.error('Failed to get ValidationTriggerService: $e');
    }
  }
}
