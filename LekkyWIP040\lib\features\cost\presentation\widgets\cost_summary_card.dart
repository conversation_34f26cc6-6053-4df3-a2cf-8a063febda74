// File: lib/features/cost/presentation/widgets/cost_summary_card.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/lekky_colors.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/providers/date_formatter_provider.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/cost_state.dart';
import '../providers/cost_provider.dart';

/// Date constraints for date picker
class DateConstraints {
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime defaultDate;

  DateConstraints({
    required this.firstDate,
    required this.lastDate,
    required this.defaultDate,
  });
}

/// Enhanced widget that displays cost information with period selection
class CostSummaryCard extends ConsumerWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color? iconColor;

  const CostSummaryCard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final costState = ref.watch(costProvider).value;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.getCostMainCardGradient(
                Theme.of(context).brightness == Brightness.dark),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                children: [
                  Icon(
                    icon,
                    color: AppColors.getAppBarTextColor('cost',
                        Theme.of(context).brightness == Brightness.dark),
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.getAppBarTextColor('cost',
                              Theme.of(context).brightness == Brightness.dark),
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Cost value
              Text(
                value,
                style: TextStyle(
                  color: AppColors.getAppBarTextColor(
                      'cost', Theme.of(context).brightness == Brightness.dark),
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),

              // Subtitle
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.getAppBarTextColor('cost',
                              Theme.of(context).brightness == Brightness.dark)
                          .withOpacity(0.9),
                    ),
              ),
              const SizedBox(height: 16),

              // Period selection buttons
              if (costState != null)
                _buildPeriodSelection(context, ref, costState),

              // Custom date range section
              if (costState != null && costState.isCustomPeriod) ...[
                const SizedBox(height: 12),
                _buildCustomDateRange(context, ref, costState),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build period selection buttons
  Widget _buildPeriodSelection(
      BuildContext context, WidgetRef ref, CostState costState) {
    final periods = ['Day', 'Week', 'Month', 'Year'];

    return Column(
      children: [
        // Period buttons row
        Row(
          children: periods.map((periodName) {
            final isSelected = _isPeriodSelected(costState, periodName);
            return Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 2.0),
                child: _buildPeriodButton(
                  context,
                  ref,
                  periodName,
                  isSelected,
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),

        // Custom date range button
        _buildCustomButton(context, ref, costState),
      ],
    );
  }

  /// Build individual period button
  Widget _buildPeriodButton(
    BuildContext context,
    WidgetRef ref,
    String periodName,
    bool isSelected,
  ) {
    return SizedBox(
      height: 36,
      child: ElevatedButton(
        onPressed: () => _onPeriodSelected(ref, periodName),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surface,
          foregroundColor: isSelected
              ? Colors.white
              : Theme.of(context).colorScheme.onSurface,
          elevation: isSelected ? 2 : 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          periodName,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// Build custom date range button
  Widget _buildCustomButton(
      BuildContext context, WidgetRef ref, CostState costState) {
    final isSelected = costState.isCustomPeriod;

    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ElevatedButton.icon(
        onPressed: () => _onCustomSelected(ref),
        icon: const Icon(
          Icons.date_range,
          size: 16,
        ),
        label: Text(
          'Custom Date Range',
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surface,
          foregroundColor: isSelected
              ? Colors.white
              : Theme.of(context).colorScheme.onSurface,
          elevation: isSelected ? 2 : 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      ),
    );
  }

  /// Build custom date range selector
  Widget _buildCustomDateRange(
      BuildContext context, WidgetRef ref, CostState costState) {
    // Show insufficient data message if needed
    if (costState.hasInsufficientData) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.getAppBarTextColor(
                      'cost', Theme.of(context).brightness == Brightness.dark)
                  .withOpacity(0.9),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              costState.insufficientDataMessage ?? 'Not enough data',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.getAppBarTextColor('cost',
                            Theme.of(context).brightness == Brightness.dark)
                        .withOpacity(0.9),
                  ),
            ),
          ],
        ),
      );
    }

    // Show date pickers when sufficient data exists
    final screenWidth = MediaQuery.of(context).size.width;
    final spacing = screenWidth < 360 ? 8.0 : 12.0;

    return Row(
      children: [
        // From date picker
        Expanded(
          child: _buildDatePicker(
            context,
            ref,
            'From',
            costState.fromDate,
            (date) => _updateFromDate(ref, date, costState.toDate),
            otherDate: costState.toDate,
          ),
        ),
        SizedBox(width: spacing),
        // To date picker
        Expanded(
          child: _buildDatePicker(
            context,
            ref,
            'To',
            costState.toDate,
            (date) => _updateToDate(ref, costState.fromDate, date),
            otherDate: costState.fromDate,
          ),
        ),
      ],
    );
  }

  /// Build individual date picker
  Widget _buildDatePicker(
    BuildContext context,
    WidgetRef ref,
    String label,
    DateTime? selectedDate,
    Function(DateTime) onDateSelected, {
    DateTime? otherDate,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.getAppBarTextColor(
                        'cost', Theme.of(context).brightness == Brightness.dark)
                    .withOpacity(0.9),
              ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 36,
          child: ElevatedButton(
            onPressed: () {
              if (context.mounted) {
                _showDatePicker(
                  context,
                  ref,
                  selectedDate,
                  onDateSelected,
                  isFromDate: label == 'From',
                  otherDate: otherDate,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isDark ? Colors.black : Colors.white,
              foregroundColor: isDark ? Colors.white : Colors.black,
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            child: Text(
              selectedDate != null
                  ? ref
                      .watch(dateFormatterProvider)
                      .formatDateForCostRange(selectedDate)
                  : 'Select',
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// Check if a period is selected
  bool _isPeriodSelected(CostState costState, String periodName) {
    if (costState.isCustomPeriod) return false;

    switch (periodName) {
      case 'Day':
        return costState.selectedPeriod == CostPeriod.futureDay;
      case 'Week':
        return costState.selectedPeriod == CostPeriod.futureWeek;
      case 'Month':
        return costState.selectedPeriod == CostPeriod.futureMonth;
      case 'Year':
        return costState.selectedPeriod == CostPeriod.futureYear;
      default:
        return false;
    }
  }

  /// Handle period selection
  void _onPeriodSelected(WidgetRef ref, String periodName) {
    CostPeriod period;
    switch (periodName) {
      case 'Day':
        period = CostPeriod.futureDay;
        break;
      case 'Week':
        period = CostPeriod.futureWeek;
        break;
      case 'Month':
        period = CostPeriod.futureMonth;
        break;
      case 'Year':
        period = CostPeriod.futureYear;
        break;
      default:
        return;
    }
    ref.read(costProvider.notifier).updatePeriod(period);
  }

  /// Handle custom period selection
  void _onCustomSelected(WidgetRef ref) {
    ref.read(costProvider.notifier).updatePeriod(CostPeriod.custom);
  }

  /// Update from date while preserving to date
  void _updateFromDate(WidgetRef ref, DateTime fromDate, DateTime? toDate) {
    ref.read(costProvider.notifier).setCustomDateRange(fromDate, toDate);
  }

  /// Update to date while preserving from date
  void _updateToDate(WidgetRef ref, DateTime? fromDate, DateTime toDate) {
    ref.read(costProvider.notifier).setCustomDateRange(fromDate, toDate);
  }

  /// Show date picker dialog with dynamic constraints
  Future<void> _showDatePicker(BuildContext context, WidgetRef ref,
      DateTime? currentDate, Function(DateTime) onDateSelected,
      {bool isFromDate = false, DateTime? otherDate}) async {
    if (!context.mounted) return;

    // Calculate dynamic constraints when date picker opens
    final constraints = isFromDate
        ? await _getFromDateConstraints(ref, otherDate)
        : await _getToDateConstraints(ref, otherDate);

    if (!context.mounted) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? constraints.defaultDate,
      firstDate: constraints.firstDate,
      lastDate: constraints.lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary:
                      Theme.of(context).extension<LekkyColors>()?.costAppBar ??
                          Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && context.mounted) {
      onDateSelected(picked);
    }
  }

  /// Get constraints for from date picker
  Future<DateConstraints> _getFromDateConstraints(
      WidgetRef ref, DateTime? toDate) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);
      final firstMeterDate = await costRepo.getFirstMeterReadingDate();
      final previousMeterDate = await costRepo.getPreviousMeterReadingDate();

      return DateConstraints(
        firstDate: firstMeterDate ?? DateTime(2020),
        lastDate: toDate ?? DateTime.now(),
        defaultDate: previousMeterDate ?? firstMeterDate ?? DateTime.now(),
      );
    } catch (e) {
      return DateConstraints(
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }

  /// Get constraints for to date picker
  Future<DateConstraints> _getToDateConstraints(
      WidgetRef ref, DateTime? fromDate) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);
      final lastMeterDate = await costRepo.getLastMeterReadingDate();

      return DateConstraints(
        firstDate: fromDate ?? DateTime(2020),
        lastDate: lastMeterDate ?? DateTime.now(),
        defaultDate: lastMeterDate ?? DateTime.now(),
      );
    } catch (e) {
      return DateConstraints(
        firstDate: fromDate ?? DateTime(2020),
        lastDate: DateTime.now(),
        defaultDate: DateTime.now(),
      );
    }
  }
}
