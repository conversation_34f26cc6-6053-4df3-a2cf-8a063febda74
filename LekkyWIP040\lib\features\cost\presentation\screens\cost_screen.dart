// File: lib/features/cost/presentation/screens/cost_screen.dart
import 'package:flutter/material.dart';
import 'riverpod_cost_screen.dart';

/// The cost screen of the app - redirects to Riverpod implementation
/// Legacy controller removed - migrated to Riverpod in Phase 5
class CostScreen extends StatelessWidget {
  const CostScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Redirect to the new Riverpod-based cost screen
    return const RiverpodCostScreen();
  }
}
