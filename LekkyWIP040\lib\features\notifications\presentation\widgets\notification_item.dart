import 'package:flutter/material.dart';
import '../../domain/models/notification.dart';
import '../../../../core/theme/app_colors.dart';

/// A widget that displays a single notification
class NotificationItem extends StatelessWidget {
  /// The notification to display
  final AppNotification notification;

  /// Callback when the notification is marked as read
  final Function(int) onMarkAsRead;

  /// Callback when the notification is deleted
  final Function(int) onDelete;

  /// Constructor
  const NotificationItem({
    super.key,
    required this.notification,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get icon based on notification type
    IconData icon;
    Color iconColor;

    switch (notification.type) {
      case NotificationType.lowBalance:
        icon = Icons.warning;
        break;
      case NotificationType.timeToTopUp:
        icon = Icons.add_card;
        break;
      case NotificationType.invalidRecord:
        icon = Icons.error;
        break;
      case NotificationType.readingReminder:
        icon = Icons.schedule;
        break;
      case NotificationType.welcome:
        icon = Icons.info;
        break;
      case NotificationType.appUpdate:
        icon = Icons.system_update;
        break;
    }

    // Get theme-aware icon color
    iconColor = AppColors.getNotificationIconColor(
        notification.type, theme.brightness == Brightness.dark);

    return Dismissible(
      key: ValueKey('notification_${notification.id}'),
      background: Container(
        color: theme.colorScheme.primary.withOpacity(0.2),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: Icon(
          Icons.check,
          color: theme.colorScheme.primary,
        ),
      ),
      secondaryBackground: Container(
        color: theme.colorScheme.error.withOpacity(0.2),
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: Icon(
          Icons.delete,
          color: theme.colorScheme.error,
        ),
      ),
      confirmDismiss: (direction) async {
        if (direction == DismissDirection.startToEnd) {
          // Mark as read
          if (!notification.isRead && notification.id != null) {
            onMarkAsRead(notification.id!);
          }
          return false;
        } else {
          // Delete
          if (notification.id != null) {
            onDelete(notification.id!);
            return true;
          }
          return false;
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: notification.isRead
              ? theme.colorScheme.surface
              : theme.colorScheme.primary.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    notification.title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: notification.isRead
                          ? FontWeight.w500
                          : FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    notification.message,
                    style: TextStyle(
                      fontSize: 13,
                      height: 1.3,
                      color: theme.colorScheme.onSurface.withOpacity(0.75),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getRelativeTimeString(notification.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Get relative time string (e.g., "2 hours ago", "Yesterday")
  String _getRelativeTimeString(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      // For older notifications, show the actual date
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
