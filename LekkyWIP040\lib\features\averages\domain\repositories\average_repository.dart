import '../../../../core/models/average.dart';

/// Repository interface for average operations
abstract class AverageRepository {
  /// Get the latest averages from the database
  Future<Average?> getLatestAverages();
  
  /// Save or update averages in the database
  Future<int> saveAverages(Average average);
  
  /// Check if averages exist in the database
  Future<bool> hasAverages();
  
  /// Delete all averages (for testing/reset purposes)
  Future<int> deleteAllAverages();
}
