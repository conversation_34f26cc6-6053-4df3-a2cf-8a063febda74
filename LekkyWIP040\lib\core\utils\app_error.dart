// File: lib/core/utils/app_error.dart
import 'error_types.dart';

/// Application error class for structured error handling
class AppError extends Error {
  /// Error message
  final String message;
  
  /// Error type
  final ErrorType type;
  
  /// Error severity
  final ErrorSeverity severity;
  
  /// Additional error details
  final dynamic details;
  
  /// Stack trace
  final StackTrace? stackTrace;
  
  /// Constructor
  AppError({
    required this.message,
    required this.type,
    required this.severity,
    this.details,
    this.stackTrace,
  });
  
  /// Create a copy with updated values
  AppError copyWith({
    String? message,
    ErrorType? type,
    ErrorSeverity? severity,
    dynamic details,
    StackTrace? stackTrace,
  }) {
    return AppError(
      message: message ?? this.message,
      type: type ?? this.type,
      severity: severity ?? this.severity,
      details: details ?? this.details,
      stackTrace: stackTrace ?? this.stackTrace,
    );
  }
  
  @override
  String toString() {
    return 'AppError: $message (${type.name}, ${severity.name})';
  }
}
