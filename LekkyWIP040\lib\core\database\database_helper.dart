import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../constants/database_constants.dart';
import '../utils/logger.dart';

/// Database helper class for managing SQLite database operations
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  /// Factory constructor
  factory DatabaseHelper() => _instance;

  /// Internal constructor
  DatabaseHelper._internal();

  /// Get the database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database
  Future<Database> _initDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path =
          join(documentsDirectory.path, DatabaseConstants.databaseName);

      // Create a backup before opening the database if it exists
      File dbFile = File(path);
      if (await dbFile.exists()) {
        await _createBackup(dbFile);
      }

      return await openDatabase(
        path,
        version: DatabaseConstants.databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      Logger.error('Failed to initialize database: $e');
      rethrow;
    }
  }

  /// Create a backup of the database file
  Future<void> _createBackup(File dbFile) async {
    try {
      final backupPath = '${dbFile.path}.backup';
      await dbFile.copy(backupPath);
      Logger.info('Database backup created at $backupPath');
    } catch (e) {
      Logger.error('Failed to create database backup: $e');
      // Continue without backup - better than no database
    }
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    await _createMeterReadingsTable(db);
    await _createTopUpsTable(db);
    await _createAveragesTable(db);
    await _createPerReadingAveragesTable(db);
    await _createSettingsTable(db);
    await _createVersionTable(db);
    await _createNotificationsTable(db);

    // Insert initial version record
    await db.insert(
      DatabaseConstants.versionTable,
      {'version': version, 'updated_at': DateTime.now().toIso8601String()},
    );
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      // Begin transaction for atomic migration
      await db.transaction((txn) async {
        // Perform migrations based on version
        if (oldVersion < 2 && newVersion >= 2) {
          await _migrateV1toV2(txn);
        }

        if (oldVersion < 3 && newVersion >= 3) {
          await _migrateV2toV3(txn);
        }

        if (oldVersion < 4 && newVersion >= 4) {
          await _migrateV3toV4(txn);
        }

        if (oldVersion < 5 && newVersion >= 5) {
          await _migrateV4toV5(txn);
        }

        if (oldVersion < 6 && newVersion >= 6) {
          await _migrateV5toV6(txn);
        }

        if (oldVersion < 7 && newVersion >= 7) {
          await _migrateV6toV7(txn);
        }

        // Update version record
        await txn.update(
          DatabaseConstants.versionTable,
          {
            'version': newVersion,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = 1',
        );
      });

      Logger.info('Database upgraded from $oldVersion to $newVersion');
    } catch (e) {
      Logger.error('Database migration failed: $e');
      rethrow;
    }
  }

  /// Create meter readings table
  Future<void> _createMeterReadingsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.meterReadingsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        value REAL NOT NULL,
        date TEXT NOT NULL,
        is_valid INTEGER NOT NULL DEFAULT 1,
        notes TEXT,
        recent_average_per_day REAL,
        is_estimated INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create index on date for faster queries
    await db.execute('''
      CREATE INDEX idx_meter_readings_date
      ON ${DatabaseConstants.meterReadingsTable} (date)
    ''');
  }

  /// Create top-ups table
  Future<void> _createTopUpsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.topUpsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        amount REAL NOT NULL,
        date TEXT NOT NULL,
        is_valid INTEGER NOT NULL DEFAULT 1,
        notes TEXT,
        payment_method TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create index on date for faster queries
    await db.execute('''
      CREATE INDEX idx_top_ups_date
      ON ${DatabaseConstants.topUpsTable} (date)
    ''');
  }

  /// Create averages table
  Future<void> _createAveragesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.averagesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recent_average REAL,
        total_average REAL,
        last_updated TEXT NOT NULL
      )
    ''');
  }

  /// Create per-reading averages table
  Future<void> _createPerReadingAveragesTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.perReadingAveragesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        meter_reading_id INTEGER NOT NULL,
        reading_date TEXT NOT NULL,
        recent_average_per_day REAL NOT NULL,
        calculated_at TEXT NOT NULL
      )
    ''');

    // Create index on meter_reading_id for faster queries
    await db.execute('''
      CREATE INDEX idx_per_reading_averages_meter_reading_id
      ON ${DatabaseConstants.perReadingAveragesTable} (meter_reading_id)
    ''');

    // Create index on reading_date for faster queries
    await db.execute('''
      CREATE INDEX idx_per_reading_averages_reading_date
      ON ${DatabaseConstants.perReadingAveragesTable} (reading_date)
    ''');
  }

  /// Create settings table
  Future<void> _createSettingsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.settingsTable} (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  /// Create version table for tracking migrations
  Future<void> _createVersionTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.versionTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version INTEGER NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');
  }

  /// Create notifications table
  Future<void> _createNotificationsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${DatabaseConstants.notificationsTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        type INTEGER NOT NULL,
        isRead INTEGER NOT NULL DEFAULT 0
      )
    ''');

    // Create index on timestamp for faster queries
    await db.execute('''
      CREATE INDEX idx_notifications_timestamp
      ON ${DatabaseConstants.notificationsTable} (timestamp)
    ''');

    // Create index on isRead for faster queries
    await db.execute('''
      CREATE INDEX idx_notifications_is_read
      ON ${DatabaseConstants.notificationsTable} (isRead)
    ''');
  }

  /// Migration from v1 to v2
  Future<void> _migrateV1toV2(Transaction txn) async {
    // Example migration: Add a new column to meter_readings
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.meterReadingsTable}
      ADD COLUMN is_estimated INTEGER NOT NULL DEFAULT 0
    ''');
  }

  /// Migration from v2 to v3
  Future<void> _migrateV2toV3(Transaction txn) async {
    // Example migration: Add a new column to top_ups
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.topUpsTable}
      ADD COLUMN payment_method TEXT
    ''');
  }

  /// Migration from v3 to v4
  Future<void> _migrateV3toV4(Transaction txn) async {
    // Add is_valid column to top_ups table for duplicate flagging
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.topUpsTable}
      ADD COLUMN is_valid INTEGER NOT NULL DEFAULT 1
    ''');
  }

  /// Migration from v4 to v5
  Future<void> _migrateV4toV5(Transaction txn) async {
    // Populate averages table with initial calculation
    await _populateAveragesTable(txn);
  }

  /// Migration from v5 to v6
  Future<void> _migrateV5toV6(Transaction txn) async {
    // Add recent_average_per_day column to meter_readings table
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.meterReadingsTable}
      ADD COLUMN recent_average_per_day REAL
    ''');

    // Create per-reading averages table
    await txn.execute('''
      CREATE TABLE ${DatabaseConstants.perReadingAveragesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        meter_reading_id INTEGER NOT NULL,
        reading_date TEXT NOT NULL,
        recent_average_per_day REAL NOT NULL,
        calculated_at TEXT NOT NULL
      )
    ''');

    // Create indexes
    await txn.execute('''
      CREATE INDEX idx_per_reading_averages_meter_reading_id
      ON ${DatabaseConstants.perReadingAveragesTable} (meter_reading_id)
    ''');

    await txn.execute('''
      CREATE INDEX idx_per_reading_averages_reading_date
      ON ${DatabaseConstants.perReadingAveragesTable} (reading_date)
    ''');

    // Calculate and populate per-reading averages
    await _populatePerReadingAveragesTable(txn);
  }

  /// Migration from v6 to v7 - Convert is_valid boolean to status integer
  Future<void> _migrateV6toV7(Transaction txn) async {
    Logger.info(
        'Migrating database from v6 to v7 - Converting to status system');

    // Add status column to meter_readings table
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.meterReadingsTable}
      ADD COLUMN status INTEGER NOT NULL DEFAULT 0
    ''');

    // Add status column to top_ups table
    await txn.execute('''
      ALTER TABLE ${DatabaseConstants.topUpsTable}
      ADD COLUMN status INTEGER NOT NULL DEFAULT 0
    ''');

    // Migrate existing data and re-validate
    await _migrateValidationData(txn);

    Logger.info('Migration v6 to v7 completed');
  }

  /// Populate averages table with initial calculation
  Future<void> _populateAveragesTable(Transaction txn) async {
    try {
      Logger.info('Populating averages table with initial calculation...');

      // Get all meter readings and top-ups
      final meterReadings = await txn.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date ASC',
      );

      final topUps = await txn.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date ASC',
      );

      if (meterReadings.length < 2) {
        Logger.info('Not enough meter readings for average calculation');
        return;
      }

      // Calculate total average using same logic as AverageCalculator
      final firstReading = meterReadings.first;
      final lastReading = meterReadings.last;

      final firstValue = firstReading['value'] as double;
      final lastValue = lastReading['value'] as double;
      final firstDate = DateTime.parse(firstReading['date'] as String);
      final lastDate = DateTime.parse(lastReading['date'] as String);

      final totalDays = lastDate.difference(firstDate).inDays;

      if (totalDays <= 0) {
        Logger.info('Invalid date range for average calculation');
        return;
      }

      // Calculate total top-ups between first and last readings
      double totalTopUps = 0;
      for (var topUp in topUps) {
        final topUpDate = DateTime.parse(topUp['date'] as String);
        if (topUpDate.isAfter(firstDate) && topUpDate.isBefore(lastDate)) {
          totalTopUps += topUp['amount'] as double;
        }
      }

      // Total usage = (first reading - last reading + top-ups)
      final totalUsage = firstValue - lastValue + totalTopUps;

      if (totalUsage <= 0) {
        Logger.info('Invalid usage calculation for averages');
        return;
      }

      final totalAverage = totalUsage / totalDays;

      // For recent average, use last 30 days or last 5 readings
      double recentAverage = totalAverage; // Default to total average

      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final recentReadings = meterReadings.where((reading) {
        final date = DateTime.parse(reading['date'] as String);
        return date.isAfter(thirtyDaysAgo);
      }).toList();

      if (recentReadings.length >= 2) {
        final recentFirst = recentReadings.first;
        final recentLast = recentReadings.last;

        final recentFirstValue = recentFirst['value'] as double;
        final recentLastValue = recentLast['value'] as double;
        final recentFirstDate = DateTime.parse(recentFirst['date'] as String);
        final recentLastDate = DateTime.parse(recentLast['date'] as String);

        final recentDays = recentLastDate.difference(recentFirstDate).inDays;

        if (recentDays > 0) {
          // Calculate recent top-ups
          double recentTopUps = 0;
          for (var topUp in topUps) {
            final topUpDate = DateTime.parse(topUp['date'] as String);
            if (topUpDate.isAfter(recentFirstDate) &&
                topUpDate.isBefore(recentLastDate)) {
              recentTopUps += topUp['amount'] as double;
            }
          }

          final recentUsage = recentFirstValue - recentLastValue + recentTopUps;
          if (recentUsage > 0) {
            recentAverage = recentUsage / recentDays;
          }
        }
      }

      // Insert the calculated averages
      await txn.insert(
        DatabaseConstants.averagesTable,
        {
          'recent_average': recentAverage,
          'total_average': totalAverage,
          'last_updated': DateTime.now().toIso8601String(),
        },
      );

      Logger.info(
          'Averages populated - Recent: $recentAverage, Total: $totalAverage');
    } catch (e) {
      Logger.error('Failed to populate averages table: $e');
      // Don't rethrow - migration should continue even if this fails
    }
  }

  /// Populate per-reading averages table with calculated values
  Future<void> _populatePerReadingAveragesTable(Transaction txn) async {
    try {
      Logger.info('Populating per-reading averages table...');

      // Get all meter readings sorted by date
      final meterReadings = await txn.query(
        DatabaseConstants.meterReadingsTable,
        orderBy: 'date ASC',
      );

      final topUps = await txn.query(
        DatabaseConstants.topUpsTable,
        orderBy: 'date ASC',
      );

      if (meterReadings.length < 2) {
        Logger.info(
            'Not enough meter readings for per-reading average calculation');
        return;
      }

      // Calculate recent average for each reading after the first
      for (int i = 1; i < meterReadings.length; i++) {
        final currentReading = meterReadings[i];
        final previousReading = meterReadings[i - 1];

        final currentValue = currentReading['value'] as double;
        final previousValue = previousReading['value'] as double;
        final currentDate = DateTime.parse(currentReading['date'] as String);
        final previousDate = DateTime.parse(previousReading['date'] as String);

        final days = currentDate.difference(previousDate).inDays;

        if (days > 0) {
          // Calculate top-ups between these two readings
          double topUpsBetween = 0;
          for (var topUp in topUps) {
            final topUpDate = DateTime.parse(topUp['date'] as String);
            if (topUpDate.isAfter(previousDate) &&
                topUpDate.isBefore(currentDate)) {
              topUpsBetween += topUp['amount'] as double;
            }
          }

          // Calculate usage: previous reading - current reading + top-ups
          final usage = previousValue - currentValue + topUpsBetween;

          if (usage > 0) {
            final recentAveragePerDay = usage / days;

            // Insert per-reading average
            await txn.insert(
              DatabaseConstants.perReadingAveragesTable,
              {
                'meter_reading_id': currentReading['id'],
                'reading_date': currentReading['date'],
                'recent_average_per_day': recentAveragePerDay,
                'calculated_at': DateTime.now().toIso8601String(),
              },
            );
          }
        }
      }

      Logger.info(
          'Per-reading averages populated for ${meterReadings.length - 1} readings');
    } catch (e) {
      Logger.error('Failed to populate per-reading averages table: $e');
      // Don't rethrow - migration should continue even if this fails
    }
  }

  /// Close the database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Reset the database (for testing purposes)
  Future<void> resetDatabase() async {
    try {
      if (_database != null) {
        await _database!.close();
        _database = null;
      }

      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path =
          join(documentsDirectory.path, DatabaseConstants.databaseName);

      // Create a backup before deleting
      File dbFile = File(path);
      if (await dbFile.exists()) {
        await _createBackup(dbFile);
        await dbFile.delete();
      }

      // Reinitialize the database
      _database = await _initDatabase();
      Logger.info('Database reset successfully');
    } catch (e) {
      Logger.error('Failed to reset database: $e');
      rethrow;
    }
  }

  /// Optimize the database
  Future<void> optimizeDatabase() async {
    try {
      final db = await database;
      await db.execute('VACUUM');
      await db.execute('ANALYZE');
      Logger.info('Database optimized successfully');
    } catch (e) {
      Logger.error('Failed to optimize database: $e');
      rethrow;
    }
  }

  /// Get database information
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final db = await database;

      // Get counts for each table
      final meterReadingsCount = Sqflite.firstIntValue(await db.rawQuery(
              'SELECT COUNT(*) FROM ${DatabaseConstants.meterReadingsTable}')) ??
          0;

      final topUpsCount = Sqflite.firstIntValue(await db.rawQuery(
              'SELECT COUNT(*) FROM ${DatabaseConstants.topUpsTable}')) ??
          0;

      final settingsCount = Sqflite.firstIntValue(await db.rawQuery(
              'SELECT COUNT(*) FROM ${DatabaseConstants.settingsTable}')) ??
          0;

      final notificationsCount = Sqflite.firstIntValue(await db.rawQuery(
              'SELECT COUNT(*) FROM ${DatabaseConstants.notificationsTable}')) ??
          0;

      // Get database version
      final versionResult = await db.query(
        DatabaseConstants.versionTable,
        orderBy: 'id DESC',
        limit: 1,
      );

      final version =
          versionResult.isNotEmpty ? versionResult.first['version'] : 'Unknown';
      final lastUpdated = versionResult.isNotEmpty
          ? versionResult.first['updated_at']
          : 'Unknown';

      return {
        'version': version,
        'last_updated': lastUpdated,
        'meter_readings_count': meterReadingsCount,
        'top_ups_count': topUpsCount,
        'settings_count': settingsCount,
        'notifications_count': notificationsCount,
        'total_entries': meterReadingsCount + topUpsCount,
      };
    } catch (e) {
      Logger.error('Failed to get database info: $e');
      return {
        'version': 'Error',
        'last_updated': 'Error',
        'meter_readings_count': 0,
        'top_ups_count': 0,
        'settings_count': 0,
        'notifications_count': 0,
        'total_entries': 0,
        'error': e.toString(),
      };
    }
  }

  /// Migrate validation data from is_valid boolean to status integer
  Future<void> _migrateValidationData(Transaction txn) async {
    try {
      Logger.info('Migrating validation data to new status system');

      // Set status based on current is_valid values for meter readings
      await txn.execute('''
        UPDATE ${DatabaseConstants.meterReadingsTable}
        SET status = CASE
          WHEN is_valid = 1 THEN 0
          ELSE 1
        END
      ''');

      // Set status based on current is_valid values for top-ups
      await txn.execute('''
        UPDATE ${DatabaseConstants.topUpsTable}
        SET status = CASE
          WHEN is_valid = 1 THEN 0
          ELSE 1
        END
      ''');

      Logger.info('Validation data migration completed');
    } catch (e) {
      Logger.error('Failed to migrate validation data: $e');
      // Don't rethrow - migration should continue even if this fails
    }
  }
}
