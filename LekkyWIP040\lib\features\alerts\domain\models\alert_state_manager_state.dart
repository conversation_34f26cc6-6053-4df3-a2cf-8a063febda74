import 'package:freezed_annotation/freezed_annotation.dart';

part 'alert_state_manager_state.freezed.dart';
part 'alert_state_manager_state.g.dart';

/// State for consolidated alert state management
@freezed
class AlertStateManagerState with _$AlertStateManagerState {
  const factory AlertStateManagerState({
    /// Last low balance alert date
    DateTime? lastLowBalanceAlertDate,
    
    /// Last time to top-up alert date
    DateTime? lastTimeToTopUpAlertDate,
    
    /// Whether state is loading
    @Default(false) bool isLoading,
    
    /// Error message if any
    String? errorMessage,
  }) = _AlertStateManagerState;

  factory AlertStateManagerState.fromJson(Map<String, dynamic> json) =>
      _$AlertStateManagerStateFromJson(json);

  /// Create initial state
  factory AlertStateManagerState.initial() => const AlertStateManagerState(
    isLoading: true,
  );
}

/// Extension methods for AlertStateManagerState
extension AlertStateManagerStateX on AlertStateManagerState {
  /// Check if low balance alert can be fired (not sent today)
  bool get canFireLowBalanceAlert {
    if (lastLowBalanceAlertDate == null) return true;
    
    final now = DateTime.now();
    final lastAlert = lastLowBalanceAlertDate!;
    
    // Check if it's a different calendar day
    return !(lastAlert.year == now.year &&
        lastAlert.month == now.month &&
        lastAlert.day == now.day);
  }
  
  /// Check if time to top-up alert can be fired (not sent today)
  bool get canFireTimeToTopUpAlert {
    if (lastTimeToTopUpAlertDate == null) return true;
    
    final now = DateTime.now();
    final lastAlert = lastTimeToTopUpAlertDate!;
    
    // Check if it's a different calendar day
    return !(lastAlert.year == now.year &&
        lastAlert.month == now.month &&
        lastAlert.day == now.day);
  }
  
  /// Get formatted last alert info for debugging
  String get debugInfo {
    final buffer = StringBuffer();
    buffer.writeln('Alert State Manager Debug Info:');
    buffer.writeln('Low Balance Alert: ${lastLowBalanceAlertDate?.toString() ?? 'Never'}');
    buffer.writeln('Time to Top-Up Alert: ${lastTimeToTopUpAlertDate?.toString() ?? 'Never'}');
    buffer.writeln('Can Fire Low Balance: $canFireLowBalanceAlert');
    buffer.writeln('Can Fire Time to Top-Up: $canFireTimeToTopUpAlert');
    return buffer.toString();
  }
}
