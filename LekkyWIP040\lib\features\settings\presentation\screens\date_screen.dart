import 'package:flutter/material.dart';

/// Date settings screen
class DateScreen extends StatelessWidget {
  /// Constructor
  const DateScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Date Settings'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date format card
            Card(
              margin: const EdgeInsets.only(bottom: 16.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Date Format',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Choose how dates are displayed in the app',
                      style: TextStyle(
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Date format radio buttons
                    RadioListTile<String>(
                      title: const Text('DD-MM-YYYY'),
                      subtitle: const Text('Example: 31-12-2023'),
                      value: 'DD-MM-YYYY',
                      groupValue: 'DD-MM-YYYY',
                      onChanged: (value) {
                        // TODO: Implement date format change with Riverpod
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('MM-DD-YYYY'),
                      subtitle: const Text('Example: 12-31-2023'),
                      value: 'MM-DD-YYYY',
                      groupValue: 'DD-MM-YYYY',
                      onChanged: (value) {
                        // TODO: Implement date format change with Riverpod
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('YYYY-MM-DD'),
                      subtitle: const Text('Example: 2023-12-31'),
                      value: 'YYYY-MM-DD',
                      groupValue: 'DD-MM-YYYY',
                      onChanged: (value) {
                        // TODO: Implement date format change with Riverpod
                      },
                    ),
                  ],
                ),
              ),
            ),

            // Time display card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.access_time, color: Colors.blue),
                        SizedBox(width: 16),
                        Text(
                          'Time Display',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Show time with date toggle
                    SwitchListTile(
                      title: const Text('Show Time with Date'),
                      subtitle:
                          const Text('Include time when displaying dates'),
                      value: false,
                      onChanged: (value) {
                        // TODO: Implement show time with date change with Riverpod
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
