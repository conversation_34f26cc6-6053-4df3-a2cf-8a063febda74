import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../shared/models/reminder_frequency.dart';

/// Service for managing reminder settings
class ReminderService extends ChangeNotifier {
  /// Whether reminders are enabled
  bool _remindersEnabled = false;
  
  /// Reminder frequency
  ReminderFrequency _reminderFrequency = ReminderFrequency.weekly;
  
  /// Reminder time (stored as minutes since midnight)
  int _reminderTime = 9 * 60; // Default to 9:00 AM
  
  /// Get whether reminders are enabled
  bool get remindersEnabled => _remindersEnabled;
  
  /// Get reminder frequency
  ReminderFrequency get reminderFrequency => _reminderFrequency;
  
  /// Get reminder time as TimeOfDay
  TimeOfDay get reminderTimeOfDay {
    final hours = _reminderTime ~/ 60;
    final minutes = _reminderTime % 60;
    return TimeOfDay(hour: hours, minute: minutes);
  }
  
  /// Constructor
  ReminderService() {
    _loadReminderSettings();
  }
  
  /// Load reminder settings from preferences
  Future<void> _loadReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _remindersEnabled = prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;
      
      final reminderFrequencyString = prefs.getString(PreferenceKeys.reminderFrequency);
      if (reminderFrequencyString != null) {
        _reminderFrequency = ReminderFrequency.values.firstWhere(
          (e) => e.toString() == reminderFrequencyString,
          orElse: () => ReminderFrequency.weekly,
        );
      }
      
      _reminderTime = prefs.getInt(PreferenceKeys.reminderTime) ?? 9 * 60;
      
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error loading reminder settings: $e');
    }
  }
  
  /// Set whether reminders are enabled
  Future<void> setRemindersEnabled(bool enabled) async {
    if (_remindersEnabled == enabled) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.remindersEnabled, enabled);
      
      _remindersEnabled = enabled;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting reminders enabled: $e');
    }
  }
  
  /// Set reminder frequency
  Future<void> setReminderFrequency(ReminderFrequency frequency) async {
    if (_reminderFrequency == frequency) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.reminderFrequency, frequency.toString());
      
      _reminderFrequency = frequency;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting reminder frequency: $e');
    }
  }
  
  /// Set reminder time
  Future<void> setReminderTime(TimeOfDay time) async {
    final newReminderTime = time.hour * 60 + time.minute;
    
    if (_reminderTime == newReminderTime) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(PreferenceKeys.reminderTime, newReminderTime);
      
      _reminderTime = newReminderTime;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting reminder time: $e');
    }
  }
}
