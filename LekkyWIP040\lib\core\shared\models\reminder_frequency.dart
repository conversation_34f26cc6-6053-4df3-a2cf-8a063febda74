/// Enum representing different reminder frequency options
enum ReminderFrequency {
  /// Daily reminders
  daily,
  
  /// Weekly reminders
  weekly,
  
  /// Bi-weekly reminders
  biWeekly,
  
  /// Monthly reminders
  monthly;
  
  /// Get a user-friendly display name
  String get displayName {
    switch (this) {
      case ReminderFrequency.daily:
        return 'Daily';
      case ReminderFrequency.weekly:
        return 'Weekly';
      case ReminderFrequency.biWeekly:
        return 'Bi-weekly';
      case ReminderFrequency.monthly:
        return 'Monthly';
    }
  }
  
  /// Get a description of the frequency
  String get description {
    switch (this) {
      case ReminderFrequency.daily:
        return 'Receive reminders every day';
      case ReminderFrequency.weekly:
        return 'Receive reminders once a week';
      case ReminderFrequency.biWeekly:
        return 'Receive reminders every two weeks';
      case ReminderFrequency.monthly:
        return 'Receive reminders once a month';
    }
  }
  
  /// Parse a string to get the corresponding ReminderFrequency
  static ReminderFrequency fromString(String value) {
    switch (value) {
      case 'daily':
        return ReminderFrequency.daily;
      case 'weekly':
        return ReminderFrequency.weekly;
      case 'bi_weekly':
        return ReminderFrequency.biWeekly;
      case 'monthly':
        return ReminderFrequency.monthly;
      default:
        return ReminderFrequency.weekly; // Default
    }
  }
  
  /// Get the string representation of the reminder frequency
  String get stringValue {
    switch (this) {
      case ReminderFrequency.daily:
        return 'daily';
      case ReminderFrequency.weekly:
        return 'weekly';
      case ReminderFrequency.biWeekly:
        return 'bi_weekly';
      case ReminderFrequency.monthly:
        return 'monthly';
    }
  }
}
