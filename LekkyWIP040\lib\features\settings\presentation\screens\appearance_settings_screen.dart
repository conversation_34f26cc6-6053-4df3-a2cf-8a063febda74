import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/theme_provider.dart' as theme_provider;
import '../../../../core/shared/models/theme_mode.dart';

/// Appearance settings screen
class AppearanceSettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const AppearanceSettingsScreen({super.key});

  @override
  ConsumerState<AppearanceSettingsScreen> createState() =>
      _AppearanceSettingsScreenState();
}

class _AppearanceSettingsScreenState
    extends ConsumerState<AppearanceSettingsScreen> {
  late AppThemeMode _selectedThemeMode;

  @override
  void initState() {
    super.initState();
    // Initialize with system default, will be updated when provider loads
    _selectedThemeMode = AppThemeMode.system;
  }

  @override
  Widget build(BuildContext context) {
    final themeAsync = ref.watch(theme_provider.themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Appearance'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: themeAsync.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('Error loading theme: $error'),
        ),
        data: (themeState) {
          // Update selected theme mode when data loads
          if (_selectedThemeMode != themeState.themeMode) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _selectedThemeMode = themeState.themeMode;
              });
            });
          }

          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Theme section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.palette, color: Colors.blue),
                          const SizedBox(width: 16),
                          const Text(
                            'Theme',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            'Current: ${_selectedThemeMode.displayName}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Choose light or dark theme',
                        style: TextStyle(
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Theme options
                      _buildThemeOption(
                        AppThemeMode.system,
                        Icons.brightness_auto,
                        'System Default',
                        'Follow system theme settings',
                      ),
                      _buildThemeOption(
                        AppThemeMode.light,
                        Icons.brightness_high,
                        'Light Mode',
                        'Always use light theme',
                      ),
                      _buildThemeOption(
                        AppThemeMode.dark,
                        Icons.brightness_4,
                        'Dark Mode',
                        'Always use dark theme',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildThemeOption(
    AppThemeMode themeMode,
    IconData icon,
    String title,
    String description,
  ) {
    return RadioListTile<AppThemeMode>(
      title: Row(
        children: [
          Icon(icon),
          const SizedBox(width: 16),
          Text(title),
        ],
      ),
      subtitle: Text(description),
      value: themeMode,
      groupValue: _selectedThemeMode,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedThemeMode = value;
          });
          ref
              .read(theme_provider.themeProvider.notifier)
              .updateThemeMode(value);
        }
      },
    );
  }
}
