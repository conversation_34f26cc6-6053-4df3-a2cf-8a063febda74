import 'package:flutter/material.dart';
import '../../../../core/widgets/lekky_button.dart';

/// A card that displays quick action buttons
class QuickActionsCard extends StatelessWidget {
  /// Callback when the Add Entry button is pressed
  final VoidCallback onAddEntry;

  /// Constructor
  const QuickActionsCard({
    super.key,
    required this.onAddEntry,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 72, // 50% taller than standard button height
      child: Lek<PERSON>Button(
        text: '+ Add Entry',
        onPressed: onAddEntry,
        type: LekkyButtonType.success,
        size: LekkyButtonSize.fullWidth,
        textStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }
}
