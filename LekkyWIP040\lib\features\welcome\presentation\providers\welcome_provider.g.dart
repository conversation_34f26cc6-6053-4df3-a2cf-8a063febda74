// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'welcome_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$welcomeHash() => r'8263ca4d84441d6c675891a762ecf67dc91ebcb8';

/// Provider for welcome screen management using Riverpod
///
/// Copied from [Welcome].
@ProviderFor(Welcome)
final welcomeProvider =
    AutoDisposeAsyncNotifierProvider<Welcome, WelcomeState>.internal(
  Welcome.new,
  name: r'welcomeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$welcomeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Welcome = AutoDisposeAsyncNotifier<WelcomeState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
