// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'error_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ErrorState {
  /// Current error being displayed
  AppError? get currentError => throw _privateConstructorUsedError;

  /// Whether to show error dialog
  bool get showErrorDialog => throw _privateConstructorUsedError;

  /// Whether to show error banner
  bool get showErrorBanner => throw _privateConstructorUsedError;

  /// List of recent errors for debugging
  List<AppError> get recentErrors => throw _privateConstructorUsedError;

  /// Whether error reporting is enabled
  bool get errorReportingEnabled => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)?
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)?
        $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ErrorState value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ErrorState value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ErrorState value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ErrorStateCopyWith<ErrorState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorStateCopyWith<$Res> {
  factory $ErrorStateCopyWith(
          ErrorState value, $Res Function(ErrorState) then) =
      _$ErrorStateCopyWithImpl<$Res, ErrorState>;
  @useResult
  $Res call(
      {AppError? currentError,
      bool showErrorDialog,
      bool showErrorBanner,
      List<AppError> recentErrors,
      bool errorReportingEnabled});

  $AppErrorCopyWith<$Res>? get currentError;
}

/// @nodoc
class _$ErrorStateCopyWithImpl<$Res, $Val extends ErrorState>
    implements $ErrorStateCopyWith<$Res> {
  _$ErrorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentError = freezed,
    Object? showErrorDialog = null,
    Object? showErrorBanner = null,
    Object? recentErrors = null,
    Object? errorReportingEnabled = null,
  }) {
    return _then(_value.copyWith(
      currentError: freezed == currentError
          ? _value.currentError
          : currentError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      showErrorDialog: null == showErrorDialog
          ? _value.showErrorDialog
          : showErrorDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorBanner: null == showErrorBanner
          ? _value.showErrorBanner
          : showErrorBanner // ignore: cast_nullable_to_non_nullable
              as bool,
      recentErrors: null == recentErrors
          ? _value.recentErrors
          : recentErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      errorReportingEnabled: null == errorReportingEnabled
          ? _value.errorReportingEnabled
          : errorReportingEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get currentError {
    if (_value.currentError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.currentError!, (value) {
      return _then(_value.copyWith(currentError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ErrorStateImplCopyWith<$Res>
    implements $ErrorStateCopyWith<$Res> {
  factory _$$ErrorStateImplCopyWith(
          _$ErrorStateImpl value, $Res Function(_$ErrorStateImpl) then) =
      __$$ErrorStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AppError? currentError,
      bool showErrorDialog,
      bool showErrorBanner,
      List<AppError> recentErrors,
      bool errorReportingEnabled});

  @override
  $AppErrorCopyWith<$Res>? get currentError;
}

/// @nodoc
class __$$ErrorStateImplCopyWithImpl<$Res>
    extends _$ErrorStateCopyWithImpl<$Res, _$ErrorStateImpl>
    implements _$$ErrorStateImplCopyWith<$Res> {
  __$$ErrorStateImplCopyWithImpl(
      _$ErrorStateImpl _value, $Res Function(_$ErrorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentError = freezed,
    Object? showErrorDialog = null,
    Object? showErrorBanner = null,
    Object? recentErrors = null,
    Object? errorReportingEnabled = null,
  }) {
    return _then(_$ErrorStateImpl(
      currentError: freezed == currentError
          ? _value.currentError
          : currentError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      showErrorDialog: null == showErrorDialog
          ? _value.showErrorDialog
          : showErrorDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      showErrorBanner: null == showErrorBanner
          ? _value.showErrorBanner
          : showErrorBanner // ignore: cast_nullable_to_non_nullable
              as bool,
      recentErrors: null == recentErrors
          ? _value._recentErrors
          : recentErrors // ignore: cast_nullable_to_non_nullable
              as List<AppError>,
      errorReportingEnabled: null == errorReportingEnabled
          ? _value.errorReportingEnabled
          : errorReportingEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ErrorStateImpl implements _ErrorState {
  const _$ErrorStateImpl(
      {this.currentError,
      this.showErrorDialog = false,
      this.showErrorBanner = false,
      final List<AppError> recentErrors = const [],
      this.errorReportingEnabled = true})
      : _recentErrors = recentErrors;

  /// Current error being displayed
  @override
  final AppError? currentError;

  /// Whether to show error dialog
  @override
  @JsonKey()
  final bool showErrorDialog;

  /// Whether to show error banner
  @override
  @JsonKey()
  final bool showErrorBanner;

  /// List of recent errors for debugging
  final List<AppError> _recentErrors;

  /// List of recent errors for debugging
  @override
  @JsonKey()
  List<AppError> get recentErrors {
    if (_recentErrors is EqualUnmodifiableListView) return _recentErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentErrors);
  }

  /// Whether error reporting is enabled
  @override
  @JsonKey()
  final bool errorReportingEnabled;

  @override
  String toString() {
    return 'ErrorState(currentError: $currentError, showErrorDialog: $showErrorDialog, showErrorBanner: $showErrorBanner, recentErrors: $recentErrors, errorReportingEnabled: $errorReportingEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorStateImpl &&
            (identical(other.currentError, currentError) ||
                other.currentError == currentError) &&
            (identical(other.showErrorDialog, showErrorDialog) ||
                other.showErrorDialog == showErrorDialog) &&
            (identical(other.showErrorBanner, showErrorBanner) ||
                other.showErrorBanner == showErrorBanner) &&
            const DeepCollectionEquality()
                .equals(other._recentErrors, _recentErrors) &&
            (identical(other.errorReportingEnabled, errorReportingEnabled) ||
                other.errorReportingEnabled == errorReportingEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentError,
      showErrorDialog,
      showErrorBanner,
      const DeepCollectionEquality().hash(_recentErrors),
      errorReportingEnabled);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorStateImplCopyWith<_$ErrorStateImpl> get copyWith =>
      __$$ErrorStateImplCopyWithImpl<_$ErrorStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)
        $default,
  ) {
    return $default(currentError, showErrorDialog, showErrorBanner,
        recentErrors, errorReportingEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)?
        $default,
  ) {
    return $default?.call(currentError, showErrorDialog, showErrorBanner,
        recentErrors, errorReportingEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            AppError? currentError,
            bool showErrorDialog,
            bool showErrorBanner,
            List<AppError> recentErrors,
            bool errorReportingEnabled)?
        $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(currentError, showErrorDialog, showErrorBanner,
          recentErrors, errorReportingEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ErrorState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ErrorState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ErrorState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _ErrorState implements ErrorState {
  const factory _ErrorState(
      {final AppError? currentError,
      final bool showErrorDialog,
      final bool showErrorBanner,
      final List<AppError> recentErrors,
      final bool errorReportingEnabled}) = _$ErrorStateImpl;

  @override

  /// Current error being displayed
  AppError? get currentError;
  @override

  /// Whether to show error dialog
  bool get showErrorDialog;
  @override

  /// Whether to show error banner
  bool get showErrorBanner;
  @override

  /// List of recent errors for debugging
  List<AppError> get recentErrors;
  @override

  /// Whether error reporting is enabled
  bool get errorReportingEnabled;
  @override
  @JsonKey(ignore: true)
  _$$ErrorStateImplCopyWith<_$ErrorStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
