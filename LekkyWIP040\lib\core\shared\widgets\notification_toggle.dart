import 'package:flutter/material.dart';

/// A widget for toggling notification settings
class NotificationToggle extends StatelessWidget {
  /// Title of the notification
  final String title;
  
  /// Description of the notification
  final String description;
  
  /// Whether the notification is enabled
  final bool isEnabled;
  
  /// Callback when the notification is toggled
  final Function(bool) onChanged;
  
  /// Whether the toggle is disabled
  final bool isDisabled;
  
  /// Constructor
  const NotificationToggle({
    super.key,
    required this.title,
    required this.description,
    required this.isEnabled,
    required this.onChanged,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: isDisabled ? 0.5 : 1.0,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: isEnabled,
              onChanged: isDisabled ? null : onChanged,
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
}
