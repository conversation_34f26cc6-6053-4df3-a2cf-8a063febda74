// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'riverpod_localization_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$riverpodLocalizationHash() =>
    r'79f40a466b66066aaed17c65f18e36d54fff54e9';

/// Provider for localization management using Riverpod
///
/// Copied from [RiverpodLocalization].
@ProviderFor(RiverpodLocalization)
final riverpodLocalizationProvider = AutoDisposeAsyncNotifierProvider<
    RiverpodLocalization, LocalizationState>.internal(
  RiverpodLocalization.new,
  name: r'riverpodLocalizationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$riverpodLocalizationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RiverpodLocalization = AutoDisposeAsyncNotifier<LocalizationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
