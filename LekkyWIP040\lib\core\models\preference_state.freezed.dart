// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'preference_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PreferenceState {
  /// Currency symbol (£, €, $, etc.)
  String get currencySymbol => throw _privateConstructorUsedError;

  /// Language code
  String get languageCode => throw _privateConstructorUsedError;

  /// Whether app has been set up
  bool get isSetupComplete => throw _privateConstructorUsedError;

  /// Whether welcome screen has been shown
  bool get hasShownWelcome => throw _privateConstructorUsedError;

  /// Alert threshold for low balance
  double get alertThreshold => throw _privateConstructorUsedError;

  /// Days in advance for notifications
  int get daysInAdvance => throw _privateConstructorUsedError;

  /// Date format preference
  String get dateFormat => throw _privateConstructorUsedError;

  /// Whether to show time with date
  bool get showTimeWithDate => throw _privateConstructorUsedError;

  /// Theme mode preference
  String get themeMode => throw _privateConstructorUsedError;

  /// Whether notifications are enabled
  bool get notificationsEnabled => throw _privateConstructorUsedError;

  /// Whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled => throw _privateConstructorUsedError;

  /// Whether time to top up alerts are enabled
  bool get timeToTopUpAlertsEnabled => throw _privateConstructorUsedError;

  /// Whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled => throw _privateConstructorUsedError;

  /// Initial meter reading value
  double get initialMeterReading => throw _privateConstructorUsedError;

  /// Whether to use 24-hour time format
  bool get use24HourFormat => throw _privateConstructorUsedError;

  /// Whether to show decimal places
  bool get showDecimalPlaces => throw _privateConstructorUsedError;

  /// Number of decimal places to show
  int get decimalPlaces => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)?
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)?
        $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PreferenceState value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PreferenceState value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PreferenceState value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PreferenceStateCopyWith<PreferenceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PreferenceStateCopyWith<$Res> {
  factory $PreferenceStateCopyWith(
          PreferenceState value, $Res Function(PreferenceState) then) =
      _$PreferenceStateCopyWithImpl<$Res, PreferenceState>;
  @useResult
  $Res call(
      {String currencySymbol,
      String languageCode,
      bool isSetupComplete,
      bool hasShownWelcome,
      double alertThreshold,
      int daysInAdvance,
      String dateFormat,
      bool showTimeWithDate,
      String themeMode,
      bool notificationsEnabled,
      bool lowBalanceAlertsEnabled,
      bool timeToTopUpAlertsEnabled,
      bool invalidRecordAlertsEnabled,
      double initialMeterReading,
      bool use24HourFormat,
      bool showDecimalPlaces,
      int decimalPlaces});
}

/// @nodoc
class _$PreferenceStateCopyWithImpl<$Res, $Val extends PreferenceState>
    implements $PreferenceStateCopyWith<$Res> {
  _$PreferenceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currencySymbol = null,
    Object? languageCode = null,
    Object? isSetupComplete = null,
    Object? hasShownWelcome = null,
    Object? alertThreshold = null,
    Object? daysInAdvance = null,
    Object? dateFormat = null,
    Object? showTimeWithDate = null,
    Object? themeMode = null,
    Object? notificationsEnabled = null,
    Object? lowBalanceAlertsEnabled = null,
    Object? timeToTopUpAlertsEnabled = null,
    Object? invalidRecordAlertsEnabled = null,
    Object? initialMeterReading = null,
    Object? use24HourFormat = null,
    Object? showDecimalPlaces = null,
    Object? decimalPlaces = null,
  }) {
    return _then(_value.copyWith(
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      isSetupComplete: null == isSetupComplete
          ? _value.isSetupComplete
          : isSetupComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      hasShownWelcome: null == hasShownWelcome
          ? _value.hasShownWelcome
          : hasShownWelcome // ignore: cast_nullable_to_non_nullable
              as bool,
      alertThreshold: null == alertThreshold
          ? _value.alertThreshold
          : alertThreshold // ignore: cast_nullable_to_non_nullable
              as double,
      daysInAdvance: null == daysInAdvance
          ? _value.daysInAdvance
          : daysInAdvance // ignore: cast_nullable_to_non_nullable
              as int,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      showTimeWithDate: null == showTimeWithDate
          ? _value.showTimeWithDate
          : showTimeWithDate // ignore: cast_nullable_to_non_nullable
              as bool,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as String,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lowBalanceAlertsEnabled: null == lowBalanceAlertsEnabled
          ? _value.lowBalanceAlertsEnabled
          : lowBalanceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToTopUpAlertsEnabled: null == timeToTopUpAlertsEnabled
          ? _value.timeToTopUpAlertsEnabled
          : timeToTopUpAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      invalidRecordAlertsEnabled: null == invalidRecordAlertsEnabled
          ? _value.invalidRecordAlertsEnabled
          : invalidRecordAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      initialMeterReading: null == initialMeterReading
          ? _value.initialMeterReading
          : initialMeterReading // ignore: cast_nullable_to_non_nullable
              as double,
      use24HourFormat: null == use24HourFormat
          ? _value.use24HourFormat
          : use24HourFormat // ignore: cast_nullable_to_non_nullable
              as bool,
      showDecimalPlaces: null == showDecimalPlaces
          ? _value.showDecimalPlaces
          : showDecimalPlaces // ignore: cast_nullable_to_non_nullable
              as bool,
      decimalPlaces: null == decimalPlaces
          ? _value.decimalPlaces
          : decimalPlaces // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PreferenceStateImplCopyWith<$Res>
    implements $PreferenceStateCopyWith<$Res> {
  factory _$$PreferenceStateImplCopyWith(_$PreferenceStateImpl value,
          $Res Function(_$PreferenceStateImpl) then) =
      __$$PreferenceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currencySymbol,
      String languageCode,
      bool isSetupComplete,
      bool hasShownWelcome,
      double alertThreshold,
      int daysInAdvance,
      String dateFormat,
      bool showTimeWithDate,
      String themeMode,
      bool notificationsEnabled,
      bool lowBalanceAlertsEnabled,
      bool timeToTopUpAlertsEnabled,
      bool invalidRecordAlertsEnabled,
      double initialMeterReading,
      bool use24HourFormat,
      bool showDecimalPlaces,
      int decimalPlaces});
}

/// @nodoc
class __$$PreferenceStateImplCopyWithImpl<$Res>
    extends _$PreferenceStateCopyWithImpl<$Res, _$PreferenceStateImpl>
    implements _$$PreferenceStateImplCopyWith<$Res> {
  __$$PreferenceStateImplCopyWithImpl(
      _$PreferenceStateImpl _value, $Res Function(_$PreferenceStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currencySymbol = null,
    Object? languageCode = null,
    Object? isSetupComplete = null,
    Object? hasShownWelcome = null,
    Object? alertThreshold = null,
    Object? daysInAdvance = null,
    Object? dateFormat = null,
    Object? showTimeWithDate = null,
    Object? themeMode = null,
    Object? notificationsEnabled = null,
    Object? lowBalanceAlertsEnabled = null,
    Object? timeToTopUpAlertsEnabled = null,
    Object? invalidRecordAlertsEnabled = null,
    Object? initialMeterReading = null,
    Object? use24HourFormat = null,
    Object? showDecimalPlaces = null,
    Object? decimalPlaces = null,
  }) {
    return _then(_$PreferenceStateImpl(
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _value.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      isSetupComplete: null == isSetupComplete
          ? _value.isSetupComplete
          : isSetupComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      hasShownWelcome: null == hasShownWelcome
          ? _value.hasShownWelcome
          : hasShownWelcome // ignore: cast_nullable_to_non_nullable
              as bool,
      alertThreshold: null == alertThreshold
          ? _value.alertThreshold
          : alertThreshold // ignore: cast_nullable_to_non_nullable
              as double,
      daysInAdvance: null == daysInAdvance
          ? _value.daysInAdvance
          : daysInAdvance // ignore: cast_nullable_to_non_nullable
              as int,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      showTimeWithDate: null == showTimeWithDate
          ? _value.showTimeWithDate
          : showTimeWithDate // ignore: cast_nullable_to_non_nullable
              as bool,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as String,
      notificationsEnabled: null == notificationsEnabled
          ? _value.notificationsEnabled
          : notificationsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lowBalanceAlertsEnabled: null == lowBalanceAlertsEnabled
          ? _value.lowBalanceAlertsEnabled
          : lowBalanceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToTopUpAlertsEnabled: null == timeToTopUpAlertsEnabled
          ? _value.timeToTopUpAlertsEnabled
          : timeToTopUpAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      invalidRecordAlertsEnabled: null == invalidRecordAlertsEnabled
          ? _value.invalidRecordAlertsEnabled
          : invalidRecordAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      initialMeterReading: null == initialMeterReading
          ? _value.initialMeterReading
          : initialMeterReading // ignore: cast_nullable_to_non_nullable
              as double,
      use24HourFormat: null == use24HourFormat
          ? _value.use24HourFormat
          : use24HourFormat // ignore: cast_nullable_to_non_nullable
              as bool,
      showDecimalPlaces: null == showDecimalPlaces
          ? _value.showDecimalPlaces
          : showDecimalPlaces // ignore: cast_nullable_to_non_nullable
              as bool,
      decimalPlaces: null == decimalPlaces
          ? _value.decimalPlaces
          : decimalPlaces // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$PreferenceStateImpl implements _PreferenceState {
  const _$PreferenceStateImpl(
      {this.currencySymbol = '£',
      this.languageCode = 'en',
      this.isSetupComplete = false,
      this.hasShownWelcome = false,
      this.alertThreshold = 5.0,
      this.daysInAdvance = 2,
      this.dateFormat = 'dd-MM-yyyy',
      this.showTimeWithDate = true,
      this.themeMode = 'system',
      this.notificationsEnabled = false,
      this.lowBalanceAlertsEnabled = false,
      this.timeToTopUpAlertsEnabled = false,
      this.invalidRecordAlertsEnabled = false,
      this.initialMeterReading = 0.0,
      this.use24HourFormat = true,
      this.showDecimalPlaces = true,
      this.decimalPlaces = 2});

  /// Currency symbol (£, €, $, etc.)
  @override
  @JsonKey()
  final String currencySymbol;

  /// Language code
  @override
  @JsonKey()
  final String languageCode;

  /// Whether app has been set up
  @override
  @JsonKey()
  final bool isSetupComplete;

  /// Whether welcome screen has been shown
  @override
  @JsonKey()
  final bool hasShownWelcome;

  /// Alert threshold for low balance
  @override
  @JsonKey()
  final double alertThreshold;

  /// Days in advance for notifications
  @override
  @JsonKey()
  final int daysInAdvance;

  /// Date format preference
  @override
  @JsonKey()
  final String dateFormat;

  /// Whether to show time with date
  @override
  @JsonKey()
  final bool showTimeWithDate;

  /// Theme mode preference
  @override
  @JsonKey()
  final String themeMode;

  /// Whether notifications are enabled
  @override
  @JsonKey()
  final bool notificationsEnabled;

  /// Whether low balance alerts are enabled
  @override
  @JsonKey()
  final bool lowBalanceAlertsEnabled;

  /// Whether time to top up alerts are enabled
  @override
  @JsonKey()
  final bool timeToTopUpAlertsEnabled;

  /// Whether invalid record alerts are enabled
  @override
  @JsonKey()
  final bool invalidRecordAlertsEnabled;

  /// Initial meter reading value
  @override
  @JsonKey()
  final double initialMeterReading;

  /// Whether to use 24-hour time format
  @override
  @JsonKey()
  final bool use24HourFormat;

  /// Whether to show decimal places
  @override
  @JsonKey()
  final bool showDecimalPlaces;

  /// Number of decimal places to show
  @override
  @JsonKey()
  final int decimalPlaces;

  @override
  String toString() {
    return 'PreferenceState(currencySymbol: $currencySymbol, languageCode: $languageCode, isSetupComplete: $isSetupComplete, hasShownWelcome: $hasShownWelcome, alertThreshold: $alertThreshold, daysInAdvance: $daysInAdvance, dateFormat: $dateFormat, showTimeWithDate: $showTimeWithDate, themeMode: $themeMode, notificationsEnabled: $notificationsEnabled, lowBalanceAlertsEnabled: $lowBalanceAlertsEnabled, timeToTopUpAlertsEnabled: $timeToTopUpAlertsEnabled, invalidRecordAlertsEnabled: $invalidRecordAlertsEnabled, initialMeterReading: $initialMeterReading, use24HourFormat: $use24HourFormat, showDecimalPlaces: $showDecimalPlaces, decimalPlaces: $decimalPlaces)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PreferenceStateImpl &&
            (identical(other.currencySymbol, currencySymbol) ||
                other.currencySymbol == currencySymbol) &&
            (identical(other.languageCode, languageCode) ||
                other.languageCode == languageCode) &&
            (identical(other.isSetupComplete, isSetupComplete) ||
                other.isSetupComplete == isSetupComplete) &&
            (identical(other.hasShownWelcome, hasShownWelcome) ||
                other.hasShownWelcome == hasShownWelcome) &&
            (identical(other.alertThreshold, alertThreshold) ||
                other.alertThreshold == alertThreshold) &&
            (identical(other.daysInAdvance, daysInAdvance) ||
                other.daysInAdvance == daysInAdvance) &&
            (identical(other.dateFormat, dateFormat) ||
                other.dateFormat == dateFormat) &&
            (identical(other.showTimeWithDate, showTimeWithDate) ||
                other.showTimeWithDate == showTimeWithDate) &&
            (identical(other.themeMode, themeMode) ||
                other.themeMode == themeMode) &&
            (identical(other.notificationsEnabled, notificationsEnabled) ||
                other.notificationsEnabled == notificationsEnabled) &&
            (identical(
                    other.lowBalanceAlertsEnabled, lowBalanceAlertsEnabled) ||
                other.lowBalanceAlertsEnabled == lowBalanceAlertsEnabled) &&
            (identical(
                    other.timeToTopUpAlertsEnabled, timeToTopUpAlertsEnabled) ||
                other.timeToTopUpAlertsEnabled == timeToTopUpAlertsEnabled) &&
            (identical(other.invalidRecordAlertsEnabled,
                    invalidRecordAlertsEnabled) ||
                other.invalidRecordAlertsEnabled ==
                    invalidRecordAlertsEnabled) &&
            (identical(other.initialMeterReading, initialMeterReading) ||
                other.initialMeterReading == initialMeterReading) &&
            (identical(other.use24HourFormat, use24HourFormat) ||
                other.use24HourFormat == use24HourFormat) &&
            (identical(other.showDecimalPlaces, showDecimalPlaces) ||
                other.showDecimalPlaces == showDecimalPlaces) &&
            (identical(other.decimalPlaces, decimalPlaces) ||
                other.decimalPlaces == decimalPlaces));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      currencySymbol,
      languageCode,
      isSetupComplete,
      hasShownWelcome,
      alertThreshold,
      daysInAdvance,
      dateFormat,
      showTimeWithDate,
      themeMode,
      notificationsEnabled,
      lowBalanceAlertsEnabled,
      timeToTopUpAlertsEnabled,
      invalidRecordAlertsEnabled,
      initialMeterReading,
      use24HourFormat,
      showDecimalPlaces,
      decimalPlaces);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PreferenceStateImplCopyWith<_$PreferenceStateImpl> get copyWith =>
      __$$PreferenceStateImplCopyWithImpl<_$PreferenceStateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)
        $default,
  ) {
    return $default(
        currencySymbol,
        languageCode,
        isSetupComplete,
        hasShownWelcome,
        alertThreshold,
        daysInAdvance,
        dateFormat,
        showTimeWithDate,
        themeMode,
        notificationsEnabled,
        lowBalanceAlertsEnabled,
        timeToTopUpAlertsEnabled,
        invalidRecordAlertsEnabled,
        initialMeterReading,
        use24HourFormat,
        showDecimalPlaces,
        decimalPlaces);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)?
        $default,
  ) {
    return $default?.call(
        currencySymbol,
        languageCode,
        isSetupComplete,
        hasShownWelcome,
        alertThreshold,
        daysInAdvance,
        dateFormat,
        showTimeWithDate,
        themeMode,
        notificationsEnabled,
        lowBalanceAlertsEnabled,
        timeToTopUpAlertsEnabled,
        invalidRecordAlertsEnabled,
        initialMeterReading,
        use24HourFormat,
        showDecimalPlaces,
        decimalPlaces);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String currencySymbol,
            String languageCode,
            bool isSetupComplete,
            bool hasShownWelcome,
            double alertThreshold,
            int daysInAdvance,
            String dateFormat,
            bool showTimeWithDate,
            String themeMode,
            bool notificationsEnabled,
            bool lowBalanceAlertsEnabled,
            bool timeToTopUpAlertsEnabled,
            bool invalidRecordAlertsEnabled,
            double initialMeterReading,
            bool use24HourFormat,
            bool showDecimalPlaces,
            int decimalPlaces)?
        $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(
          currencySymbol,
          languageCode,
          isSetupComplete,
          hasShownWelcome,
          alertThreshold,
          daysInAdvance,
          dateFormat,
          showTimeWithDate,
          themeMode,
          notificationsEnabled,
          lowBalanceAlertsEnabled,
          timeToTopUpAlertsEnabled,
          invalidRecordAlertsEnabled,
          initialMeterReading,
          use24HourFormat,
          showDecimalPlaces,
          decimalPlaces);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PreferenceState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PreferenceState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PreferenceState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _PreferenceState implements PreferenceState {
  const factory _PreferenceState(
      {final String currencySymbol,
      final String languageCode,
      final bool isSetupComplete,
      final bool hasShownWelcome,
      final double alertThreshold,
      final int daysInAdvance,
      final String dateFormat,
      final bool showTimeWithDate,
      final String themeMode,
      final bool notificationsEnabled,
      final bool lowBalanceAlertsEnabled,
      final bool timeToTopUpAlertsEnabled,
      final bool invalidRecordAlertsEnabled,
      final double initialMeterReading,
      final bool use24HourFormat,
      final bool showDecimalPlaces,
      final int decimalPlaces}) = _$PreferenceStateImpl;

  @override

  /// Currency symbol (£, €, $, etc.)
  String get currencySymbol;
  @override

  /// Language code
  String get languageCode;
  @override

  /// Whether app has been set up
  bool get isSetupComplete;
  @override

  /// Whether welcome screen has been shown
  bool get hasShownWelcome;
  @override

  /// Alert threshold for low balance
  double get alertThreshold;
  @override

  /// Days in advance for notifications
  int get daysInAdvance;
  @override

  /// Date format preference
  String get dateFormat;
  @override

  /// Whether to show time with date
  bool get showTimeWithDate;
  @override

  /// Theme mode preference
  String get themeMode;
  @override

  /// Whether notifications are enabled
  bool get notificationsEnabled;
  @override

  /// Whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled;
  @override

  /// Whether time to top up alerts are enabled
  bool get timeToTopUpAlertsEnabled;
  @override

  /// Whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled;
  @override

  /// Initial meter reading value
  double get initialMeterReading;
  @override

  /// Whether to use 24-hour time format
  bool get use24HourFormat;
  @override

  /// Whether to show decimal places
  bool get showDecimalPlaces;
  @override

  /// Number of decimal places to show
  int get decimalPlaces;
  @override
  @JsonKey(ignore: true)
  _$$PreferenceStateImplCopyWith<_$PreferenceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
