import 'package:flutter_test/flutter_test.dart';
import 'package:lekky/features/home/<USER>/models/dashboard_state.dart';
import 'package:lekky/features/meter_readings/domain/models/meter_reading.dart';

void main() {
  group('DashboardState', () {
    group('calculateDaysRemaining', () {
      test('should return null when no meter reading exists', () {
        // Arrange
        const dashboardState = DashboardState(
          latestMeterReading: null,
          recentAverageDailyUsage: 2.0,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isNull);
      });

      test('should return null when no recent average usage exists', () {
        // Arrange
        final meterReading = MeterReading(
          value: 100.0,
          date: DateTime.now().subtract(const Duration(days: 5)),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          recentAverageDailyUsage: null,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isNull);
      });

      test('should return null when average usage is zero', () {
        // Arrange
        final meterReading = MeterReading(
          value: 100.0,
          date: DateTime.now().subtract(const Duration(days: 5)),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          recentAverageDailyUsage: 0.0,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isNull);
      });

      test('should calculate days remaining correctly with top-ups', () {
        // Arrange
        final meterReading = MeterReading(
          value: 100.0,
          date: DateTime.now().subtract(const Duration(days: 5)),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          recentAverageDailyUsage: 2.0,
          totalTopUpsAfterLatestReading: 20.0,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isA<double>());
        expect(result, greaterThan(0.0));
        // Projected balance: 100 + 20 - (5 * 2) = 110
        // Days remaining: 110 / 2 = 55
        expect(result, closeTo(55.0, 5.0)); // Allow tolerance for precision
      });

      test('should handle recent readings correctly', () {
        // Arrange
        final meterReading = MeterReading(
          value: 100.0,
          date: DateTime.now().subtract(const Duration(minutes: 30)),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          recentAverageDailyUsage: 2.0,
          totalTopUpsAfterLatestReading: 20.0,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isA<double>());
        // For recent readings, projected balance = 100 + 20 = 120
        // Days remaining: 120 / 2 = 60
        expect(result, equals(60.0));
      });

      test('should handle zero projected balance', () {
        // Arrange
        final meterReading = MeterReading(
          value: 10.0,
          date: DateTime.now().subtract(const Duration(days: 10)),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          recentAverageDailyUsage: 5.0,
          totalTopUpsAfterLatestReading: 5.0,
        );

        // Act
        final result = dashboardState.calculateDaysRemaining();

        // Assert
        expect(result, isA<double>());
        // Projected balance would be negative, clamped to 0
        // Days remaining: 0 / 5 = 0
        expect(result, equals(0.0));
      });
    });

    group('currentBalance', () {
      test('should return zero when no meter reading exists', () {
        // Arrange
        const dashboardState = DashboardState(
          latestMeterReading: null,
          totalTopUpsAfterLatestReading: 20.0,
        );

        // Act
        final result = dashboardState.currentBalance;

        // Assert
        expect(result, equals(0.0));
      });

      test('should calculate current balance correctly', () {
        // Arrange
        final meterReading = MeterReading(
          value: 100.0,
          date: DateTime.now(),
        );
        final dashboardState = DashboardState(
          latestMeterReading: meterReading,
          totalTopUpsAfterLatestReading: 20.0,
        );

        // Act
        final result = dashboardState.currentBalance;

        // Assert
        expect(result, equals(120.0)); // 100 + 20
      });
    });
  });
}
