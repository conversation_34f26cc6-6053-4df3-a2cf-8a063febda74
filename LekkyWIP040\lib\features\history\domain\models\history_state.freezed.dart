// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'history_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HistoryState {
  /// List of entries (both meter readings and top-ups)
  List<dynamic> get entries => throw _privateConstructorUsedError;

  /// Current page
  int get currentPage => throw _privateConstructorUsedError;

  /// Total number of pages
  int get totalPages => throw _privateConstructorUsedError;

  /// Entries per page
  int get entriesPerPage => throw _privateConstructorUsedError;

  /// Current filter type
  EntryFilterType get filterType => throw _privateConstructorUsedError;

  /// Current sort order
  EntrySortOrder get sortOrder => throw _privateConstructorUsedError;

  /// Date range filter - start date
  DateTime? get startDate => throw _privateConstructorUsedError;

  /// Date range filter - end date
  DateTime? get endDate => throw _privateConstructorUsedError;

  /// Loading state
  bool get isLoading => throw _privateConstructorUsedError;

  /// Error message
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Total count of all entries (for pagination calculation)
  int get totalEntries => throw _privateConstructorUsedError;

  /// Whether pagination controls should be visible
  bool get showPaginationControls => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)?
        $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)?
        $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_HistoryState value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_HistoryState value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_HistoryState value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $HistoryStateCopyWith<HistoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoryStateCopyWith<$Res> {
  factory $HistoryStateCopyWith(
          HistoryState value, $Res Function(HistoryState) then) =
      _$HistoryStateCopyWithImpl<$Res, HistoryState>;
  @useResult
  $Res call(
      {List<dynamic> entries,
      int currentPage,
      int totalPages,
      int entriesPerPage,
      EntryFilterType filterType,
      EntrySortOrder sortOrder,
      DateTime? startDate,
      DateTime? endDate,
      bool isLoading,
      String? errorMessage,
      int totalEntries,
      bool showPaginationControls});
}

/// @nodoc
class _$HistoryStateCopyWithImpl<$Res, $Val extends HistoryState>
    implements $HistoryStateCopyWith<$Res> {
  _$HistoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? entries = null,
    Object? currentPage = null,
    Object? totalPages = null,
    Object? entriesPerPage = null,
    Object? filterType = null,
    Object? sortOrder = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? totalEntries = null,
    Object? showPaginationControls = null,
  }) {
    return _then(_value.copyWith(
      entries: null == entries
          ? _value.entries
          : entries // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      entriesPerPage: null == entriesPerPage
          ? _value.entriesPerPage
          : entriesPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      filterType: null == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as EntryFilterType,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as EntrySortOrder,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      totalEntries: null == totalEntries
          ? _value.totalEntries
          : totalEntries // ignore: cast_nullable_to_non_nullable
              as int,
      showPaginationControls: null == showPaginationControls
          ? _value.showPaginationControls
          : showPaginationControls // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoryStateImplCopyWith<$Res>
    implements $HistoryStateCopyWith<$Res> {
  factory _$$HistoryStateImplCopyWith(
          _$HistoryStateImpl value, $Res Function(_$HistoryStateImpl) then) =
      __$$HistoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<dynamic> entries,
      int currentPage,
      int totalPages,
      int entriesPerPage,
      EntryFilterType filterType,
      EntrySortOrder sortOrder,
      DateTime? startDate,
      DateTime? endDate,
      bool isLoading,
      String? errorMessage,
      int totalEntries,
      bool showPaginationControls});
}

/// @nodoc
class __$$HistoryStateImplCopyWithImpl<$Res>
    extends _$HistoryStateCopyWithImpl<$Res, _$HistoryStateImpl>
    implements _$$HistoryStateImplCopyWith<$Res> {
  __$$HistoryStateImplCopyWithImpl(
      _$HistoryStateImpl _value, $Res Function(_$HistoryStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? entries = null,
    Object? currentPage = null,
    Object? totalPages = null,
    Object? entriesPerPage = null,
    Object? filterType = null,
    Object? sortOrder = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
    Object? totalEntries = null,
    Object? showPaginationControls = null,
  }) {
    return _then(_$HistoryStateImpl(
      entries: null == entries
          ? _value._entries
          : entries // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      entriesPerPage: null == entriesPerPage
          ? _value.entriesPerPage
          : entriesPerPage // ignore: cast_nullable_to_non_nullable
              as int,
      filterType: null == filterType
          ? _value.filterType
          : filterType // ignore: cast_nullable_to_non_nullable
              as EntryFilterType,
      sortOrder: null == sortOrder
          ? _value.sortOrder
          : sortOrder // ignore: cast_nullable_to_non_nullable
              as EntrySortOrder,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      totalEntries: null == totalEntries
          ? _value.totalEntries
          : totalEntries // ignore: cast_nullable_to_non_nullable
              as int,
      showPaginationControls: null == showPaginationControls
          ? _value.showPaginationControls
          : showPaginationControls // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$HistoryStateImpl implements _HistoryState {
  const _$HistoryStateImpl(
      {final List<dynamic> entries = const [],
      this.currentPage = 0,
      this.totalPages = 1,
      this.entriesPerPage = 20,
      this.filterType = EntryFilterType.all,
      this.sortOrder = EntrySortOrder.newestFirst,
      this.startDate,
      this.endDate,
      this.isLoading = false,
      this.errorMessage,
      this.totalEntries = 0,
      this.showPaginationControls = false})
      : _entries = entries;

  /// List of entries (both meter readings and top-ups)
  final List<dynamic> _entries;

  /// List of entries (both meter readings and top-ups)
  @override
  @JsonKey()
  List<dynamic> get entries {
    if (_entries is EqualUnmodifiableListView) return _entries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_entries);
  }

  /// Current page
  @override
  @JsonKey()
  final int currentPage;

  /// Total number of pages
  @override
  @JsonKey()
  final int totalPages;

  /// Entries per page
  @override
  @JsonKey()
  final int entriesPerPage;

  /// Current filter type
  @override
  @JsonKey()
  final EntryFilterType filterType;

  /// Current sort order
  @override
  @JsonKey()
  final EntrySortOrder sortOrder;

  /// Date range filter - start date
  @override
  final DateTime? startDate;

  /// Date range filter - end date
  @override
  final DateTime? endDate;

  /// Loading state
  @override
  @JsonKey()
  final bool isLoading;

  /// Error message
  @override
  final String? errorMessage;

  /// Total count of all entries (for pagination calculation)
  @override
  @JsonKey()
  final int totalEntries;

  /// Whether pagination controls should be visible
  @override
  @JsonKey()
  final bool showPaginationControls;

  @override
  String toString() {
    return 'HistoryState(entries: $entries, currentPage: $currentPage, totalPages: $totalPages, entriesPerPage: $entriesPerPage, filterType: $filterType, sortOrder: $sortOrder, startDate: $startDate, endDate: $endDate, isLoading: $isLoading, errorMessage: $errorMessage, totalEntries: $totalEntries, showPaginationControls: $showPaginationControls)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoryStateImpl &&
            const DeepCollectionEquality().equals(other._entries, _entries) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.entriesPerPage, entriesPerPage) ||
                other.entriesPerPage == entriesPerPage) &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.sortOrder, sortOrder) ||
                other.sortOrder == sortOrder) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.totalEntries, totalEntries) ||
                other.totalEntries == totalEntries) &&
            (identical(other.showPaginationControls, showPaginationControls) ||
                other.showPaginationControls == showPaginationControls));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_entries),
      currentPage,
      totalPages,
      entriesPerPage,
      filterType,
      sortOrder,
      startDate,
      endDate,
      isLoading,
      errorMessage,
      totalEntries,
      showPaginationControls);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoryStateImplCopyWith<_$HistoryStateImpl> get copyWith =>
      __$$HistoryStateImplCopyWithImpl<_$HistoryStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)
        $default,
  ) {
    return $default(
        entries,
        currentPage,
        totalPages,
        entriesPerPage,
        filterType,
        sortOrder,
        startDate,
        endDate,
        isLoading,
        errorMessage,
        totalEntries,
        showPaginationControls);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)?
        $default,
  ) {
    return $default?.call(
        entries,
        currentPage,
        totalPages,
        entriesPerPage,
        filterType,
        sortOrder,
        startDate,
        endDate,
        isLoading,
        errorMessage,
        totalEntries,
        showPaginationControls);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<dynamic> entries,
            int currentPage,
            int totalPages,
            int entriesPerPage,
            EntryFilterType filterType,
            EntrySortOrder sortOrder,
            DateTime? startDate,
            DateTime? endDate,
            bool isLoading,
            String? errorMessage,
            int totalEntries,
            bool showPaginationControls)?
        $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(
          entries,
          currentPage,
          totalPages,
          entriesPerPage,
          filterType,
          sortOrder,
          startDate,
          endDate,
          isLoading,
          errorMessage,
          totalEntries,
          showPaginationControls);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_HistoryState value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_HistoryState value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_HistoryState value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _HistoryState implements HistoryState {
  const factory _HistoryState(
      {final List<dynamic> entries,
      final int currentPage,
      final int totalPages,
      final int entriesPerPage,
      final EntryFilterType filterType,
      final EntrySortOrder sortOrder,
      final DateTime? startDate,
      final DateTime? endDate,
      final bool isLoading,
      final String? errorMessage,
      final int totalEntries,
      final bool showPaginationControls}) = _$HistoryStateImpl;

  @override

  /// List of entries (both meter readings and top-ups)
  List<dynamic> get entries;
  @override

  /// Current page
  int get currentPage;
  @override

  /// Total number of pages
  int get totalPages;
  @override

  /// Entries per page
  int get entriesPerPage;
  @override

  /// Current filter type
  EntryFilterType get filterType;
  @override

  /// Current sort order
  EntrySortOrder get sortOrder;
  @override

  /// Date range filter - start date
  DateTime? get startDate;
  @override

  /// Date range filter - end date
  DateTime? get endDate;
  @override

  /// Loading state
  bool get isLoading;
  @override

  /// Error message
  String? get errorMessage;
  @override

  /// Total count of all entries (for pagination calculation)
  int get totalEntries;
  @override

  /// Whether pagination controls should be visible
  bool get showPaginationControls;
  @override
  @JsonKey(ignore: true)
  _$$HistoryStateImplCopyWith<_$HistoryStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
