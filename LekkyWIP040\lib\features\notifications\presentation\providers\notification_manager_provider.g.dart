// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_manager_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$notificationManagerHash() =>
    r'9f6e6c6e6c6e6c6e6c6e6c6e6c6e6c6e6c6e6c6e';

/// Provider that manages notification firing and coordination
///
/// Copied from [NotificationManager].
@ProviderFor(NotificationManager)
final notificationManagerProvider =
    AutoDisposeAsyncNotifierProvider<NotificationManager, void>.internal(
  NotificationManager.new,
  name: r'notificationManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NotificationManager = AutoDisposeAsyncNotifier<void>;
