// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cost_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$costRepositoryHash() => r'9aa089c7a3ab1bf6de438adfc086addcee79a6b0';

/// Cost repository provider
///
/// Copied from [costRepository].
@ProviderFor(costRepository)
final costRepositoryProvider = AutoDisposeProvider<CostRepository>.internal(
  costRepository,
  name: r'costRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$costRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CostRepositoryRef = AutoDisposeProviderRef<CostRepository>;
String _$costHash() => r'4dbd78bf8d329ec8968de2348e5ba9ee8e4547b1';

/// Cost analysis provider with comprehensive cost calculation and chart management
///
/// Copied from [Cost].
@ProviderFor(Cost)
final costProvider = AutoDisposeAsyncNotifierProvider<Cost, CostState>.internal(
  Cost.new,
  name: r'costProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$costHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Cost = AutoDisposeAsyncNotifier<CostState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
