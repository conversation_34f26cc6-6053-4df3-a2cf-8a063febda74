import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/date_formatter_service.dart';
import 'settings_provider.dart';

/// Provider for settings-aware date formatting
final dateFormatterProvider = Provider<DateFormatterService>((ref) {
  final settingsAsync = ref.watch(settingsProvider);

  return settingsAsync.when(
    data: (settings) => DateFormatterService(settings),
    loading: () => DateFormatterService.withDefaults(),
    error: (_, __) => DateFormatterService.withDefaults(),
  );
});
