import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';

/// Provider for managing app localization
class LocalizationProvider extends ChangeNotifier {
  /// Current locale
  Locale _locale = const Locale('en');

  /// Get current locale
  Locale get locale => _locale;

  /// Set locale
  Future<void> setLocale(String languageCode) async {
    if (_locale.languageCode == languageCode) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, languageCode);

      _locale = Locale(languageCode);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting locale: $e');
    }
  }

  /// Load locale from preferences
  Future<void> loadLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final language = prefs.getString(PreferenceKeys.language);

      if (language != null) {
        _locale = Locale(_getLanguageCode(language));
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading locale: $e');
    }
  }

  /// Detect and set device locale
  Future<void> detectAndSetDeviceLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasSetLanguage = prefs.containsKey(PreferenceKeys.language);

      // Only set device locale if user hasn't already set a language preference
      if (!hasSetLanguage) {
        final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
        if (deviceLocale.languageCode.isNotEmpty) {
          await setLocale(deviceLocale.languageCode);
        }
      } else {
        await loadLocale();
      }
    } catch (e) {
      debugPrint('Error detecting device locale: $e');
    }
  }

  /// Convert language name to language code
  String _getLanguageCode(String language) {
    switch (language) {
      case 'English':
        return 'en';
      case 'Spanish':
        return 'es';
      case 'French':
        return 'fr';
      case 'German':
        return 'de';
      case 'Italian':
        return 'it';
      case 'Portuguese':
        return 'pt';
      case 'Russian':
        return 'ru';
      case 'Chinese':
        return 'zh';
      case 'Japanese':
        return 'ja';
      case 'Hindi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// Convert language code to language name
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'es':
        return 'Spanish';
      case 'fr':
        return 'French';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'hi':
        return 'Hindi';
      default:
        return 'English';
    }
  }
}
