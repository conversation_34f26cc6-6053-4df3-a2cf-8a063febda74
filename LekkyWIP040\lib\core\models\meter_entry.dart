// File: lib/core/models/meter_entry.dart

/// Base class for meter entries (readings and top-ups)
class MeterEntry {
  /// Unique identifier for the entry
  final int? id;

  /// Date and time of the entry
  final DateTime date;

  /// Optional notes for the entry
  final String? notes;

  /// Meter reading value (0 for top-ups)
  final double reading;

  /// Amount topped up (0 for meter readings)
  final double amountToppedUp;

  /// Type code (0 = Meter Reading, 1 = Top-up)
  final int typeCode;

  /// Short-term average after this top-up
  final double? shortAverageAfterTopUp;

  /// Total average up to this point
  final double? totalAverageUpToThisPoint;

  /// Constructor
  const MeterEntry({
    this.id,
    required this.date,
    required this.reading,
    required this.amountToppedUp,
    required this.typeCode,
    this.notes,
    this.shortAverageAfterTopUp,
    this.totalAverageUpToThisPoint,
  });

  /// Create a meter entry from type code and amount
  static MeterEntry fromTypeCodeAndAmount({
    int? id,
    required int typeCode,
    required double amount,
    required DateTime timestamp,
    String? notes,
    double? shortAverageAfterTopUp,
    double? totalAverageUpToThisPoint,
  }) {
    return MeterEntry(
      id: id,
      date: timestamp,
      reading: typeCode == 0 ? amount : 0,
      amountToppedUp: typeCode == 1 ? amount : 0,
      typeCode: typeCode,
      notes: notes,
      shortAverageAfterTopUp: shortAverageAfterTopUp,
      totalAverageUpToThisPoint: totalAverageUpToThisPoint,
    );
  }

  /// Get the timestamp (alias for date)
  DateTime get timestamp => date;

  /// Get the type of entry as a string
  String get typeString => isReading ? 'Meter Reading' : 'Top-up';

  /// Get the display name for the type
  String get typeDisplayName => isReading ? 'Meter Reading' : 'Top-up';

  /// Get the value or amount of the entry
  double get value => isReading ? reading : amountToppedUp;

  /// Check if this is a meter reading
  bool get isMeterReading => typeCode == 0;

  /// Check if this is a reading (alias for isMeterReading)
  bool get isReading => typeCode == 0;

  /// Check if this is a top-up
  bool get isTopUp => typeCode == 1;

  /// Convert to a map for database storage
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'reading': reading,
      'amountToppedUp': amountToppedUp,
      'typeCode': typeCode,
      'notes': notes,
      'shortAverageAfterTopUp': shortAverageAfterTopUp,
      'totalAverageUpToThisPoint': totalAverageUpToThisPoint,
    };
  }

  /// Create a copy with updated values
  MeterEntry copyWith({
    int? id,
    DateTime? date,
    double? reading,
    double? amountToppedUp,
    int? typeCode,
    String? notes,
    double? shortAverageAfterTopUp,
    double? totalAverageUpToThisPoint,
  }) {
    return MeterEntry(
      id: id ?? this.id,
      date: date ?? this.date,
      reading: reading ?? this.reading,
      amountToppedUp: amountToppedUp ?? this.amountToppedUp,
      typeCode: typeCode ?? this.typeCode,
      notes: notes ?? this.notes,
      shortAverageAfterTopUp:
          shortAverageAfterTopUp ?? this.shortAverageAfterTopUp,
      totalAverageUpToThisPoint:
          totalAverageUpToThisPoint ?? this.totalAverageUpToThisPoint,
    );
  }

  @override
  String toString() {
    return 'MeterEntry{id: $id, date: $date, typeCode: $typeCode, reading: $reading, amountToppedUp: $amountToppedUp, notes: $notes}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MeterEntry &&
        other.id == id &&
        other.date == date &&
        other.reading == reading &&
        other.amountToppedUp == amountToppedUp &&
        other.typeCode == typeCode &&
        other.notes == notes;
  }

  @override
  int get hashCode =>
      id.hashCode ^
      date.hashCode ^
      reading.hashCode ^
      amountToppedUp.hashCode ^
      typeCode.hashCode ^
      notes.hashCode;
}
