import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';

import '../../../../core/utils/notification_debug_helper.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../../../core/widgets/lekky_button.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../alerts/presentation/providers/alert_state_manager_provider.dart';

/// Notification utilities screen for user-accessible notification management
class NotificationUtilitiesScreen extends ConsumerStatefulWidget {
  /// Constructor
  const NotificationUtilitiesScreen({super.key});

  @override
  ConsumerState<NotificationUtilitiesScreen> createState() =>
      _NotificationUtilitiesScreenState();
}

class _NotificationUtilitiesScreenState
    extends ConsumerState<NotificationUtilitiesScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Banner with back arrow
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: AppBanner(
              message: '← Notification Utilities',
              gradientColors: AppColors.getSettingsMainCardGradient(
                  Theme.of(context).brightness == Brightness.dark),
              textColor: AppColors.getAppBarTextColor(
                  'settings', Theme.of(context).brightness == Brightness.dark),
            ),
          ),
          Expanded(
            child: ref.watch(settingsProvider).when(
                  data: (settings) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Enable Notifications Card
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(Icons.notifications_active,
                                          color: Colors.blue, size: 24),
                                      const SizedBox(width: 16),
                                      const Text(
                                        'Enable Notifications',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Enable different types of notifications to stay informed about your meter status.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 16),
                                  _buildNotificationTypesList(settings),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Utilities Card
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(Icons.build,
                                          color: Colors.blue, size: 24),
                                      const SizedBox(width: 16),
                                      const Text(
                                        'Utilities',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Manage notification permissions and data.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 16),

                                  // Request Permissions button
                                  LekkyButton(
                                    text: 'Request Permissions',
                                    type: LekkyButtonType.primary,
                                    size: LekkyButtonSize.fullWidth,
                                    onPressed:
                                        _isLoading ? null : _requestPermissions,
                                    isLoading: _isLoading,
                                  ),

                                  const SizedBox(height: 8),

                                  // Clear All Data button
                                  LekkyButton(
                                    text: 'Clear All Notification Data',
                                    type: LekkyButtonType.destructive,
                                    size: LekkyButtonSize.fullWidth,
                                    onPressed:
                                        _isLoading ? null : _clearAllData,
                                    isLoading: _isLoading,
                                  ),

                                  const SizedBox(height: 12),

                                  // Reset Alert States button
                                  LekkyButton(
                                    text: 'Reset All Alert States',
                                    type: LekkyButtonType.secondary,
                                    size: LekkyButtonSize.fullWidth,
                                    onPressed:
                                        _isLoading ? null : _resetAlertStates,
                                    isLoading: _isLoading,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, _) => Center(
                    child: Text('Error loading settings: $error'),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  /// Build notification types list
  Widget _buildNotificationTypesList(dynamic settings) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available Notification Types:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildNotificationTypeRow(
            Icons.battery_alert,
            'Low Balance Alerts',
            settings.lowBalanceAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.lowBalanceAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.schedule,
            'Time to Top-Up Alerts',
            settings.timeToTopUpAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.timeToTopUpAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.error,
            'Invalid Record Alerts',
            settings.invalidRecordAlertsEnabled ? 'Enabled' : 'Disabled',
            settings.invalidRecordAlertsEnabled,
          ),
          const SizedBox(height: 4),
          _buildNotificationTypeRow(
            Icons.alarm,
            'Meter Reminders',
            settings.remindersEnabled ? 'Enabled' : 'Disabled',
            settings.remindersEnabled,
          ),
        ],
      ),
    );
  }

  /// Build notification type row
  Widget _buildNotificationTypeRow(
    IconData icon,
    String title,
    String status,
    bool isEnabled,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isEnabled ? Colors.green : Colors.grey,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        Text(
          status,
          style: TextStyle(
            fontSize: 12,
            color: isEnabled ? Colors.green : Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final permissionManager = NotificationPermissionManager();
      final granted = await permissionManager.requestPermission(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              granted
                  ? 'Notification permissions granted'
                  : 'Notification permissions denied',
            ),
            backgroundColor: granted ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Clear all notification data
  Future<void> _clearAllData() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notification Data'),
        content: const Text(
          'This will clear all notification settings, history, and cached data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final debugHelper = NotificationDebugHelper();
      await debugHelper.clearAllNotificationData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All notification data cleared successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Reset all time-based alert states (low balance, time to top-up, reminders)
  Future<void> _resetAlertStates() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Alert States'),
        content: const Text(
          'This will reset the timing for low balance alerts, time to top-up alerts, and meter reminders. They will be able to fire again immediately if conditions are met. Invalid entry alerts are not affected.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Use AlertStateManager to reset time-based alerts
      final alertStateManager = ref.read(alertStateManagerProvider.notifier);
      await alertStateManager.resetAllTimeBasedAlerts();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Alert states reset successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error resetting alert states: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
