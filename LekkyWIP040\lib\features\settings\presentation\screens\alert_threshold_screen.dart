import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';

/// Alert Threshold settings screen
class AlertThresholdScreen extends ConsumerWidget {
  /// Constructor
  const AlertThresholdScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Banner with back arrow
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: AppBanner(
                      message: '← Alert Threshold',
                      gradientColors: AppColors.getSettingsMainCardGradient(
                          Theme.of(context).brightness == Brightness.dark),
                      textColor: AppColors.getAppBarTextColor('settings',
                          Theme.of(context).brightness == Brightness.dark),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Alert settings card
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SettingsSectionHeader(
                                    title: 'Alert Threshold',
                                    description:
                                        'Configure when you want to receive alerts.',
                                    icon: Icons.notifications,
                                  ),

                                  // Alert Threshold Subsection
                                  const Text(
                                    'Alert Threshold',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  const Text(
                                    'Get notified when your balance falls below this amount.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),

                                  CurrencyInputField(
                                    value: settings.alertThreshold,
                                    onChanged: (value) => value != null
                                        ? ref
                                            .read(settingsProvider.notifier)
                                            .updateAlertThreshold(value)
                                        : null,
                                    currencySymbol: settings.currencySymbol,
                                    labelText: 'Alert Threshold',
                                    hintText: 'Enter amount',
                                    minValue: 1.00,
                                    maxValue: 999.99,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
