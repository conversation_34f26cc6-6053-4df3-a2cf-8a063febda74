import '../di/service_locator.dart';
import '../utils/logger.dart';
import '../utils/date_time_utils.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';

/// Service for generating dynamic reminder notification content
class ReminderContentGenerator {
  static final ReminderContentGenerator _instance =
      ReminderContentGenerator._internal();

  factory ReminderContentGenerator() => _instance;
  ReminderContentGenerator._internal();

  /// Generate dynamic reminder notification content
  Future<Map<String, String>> generateReminderContent() async {
    try {
      final meterData = await _getMeterReadingData();

      if (meterData != null) {
        return _generateContentWithMeterData(meterData);
      } else {
        return _generateFallbackContent();
      }
    } catch (e) {
      Logger.error('Error generating reminder content: $e');
      return _generateFallbackContent();
    }
  }

  /// Get meter reading data for content generation
  Future<Map<String, dynamic>?> _getMeterReadingData() async {
    try {
      final meterReadingRepository = serviceLocator<MeterReadingRepository>();
      final latestReading =
          await meterReadingRepository.getLatestMeterReading();

      if (latestReading == null) return null;

      final daysSinceLastReading = DateTimeUtils.calculateDaysWithPrecision(
        latestReading.date,
        DateTime.now(),
      );

      return {
        'lastReadingDate': latestReading.date,
        'lastReadingValue': latestReading.value,
        'daysSinceLastReading': daysSinceLastReading,
      };
    } catch (e) {
      Logger.error('Error getting meter reading data: $e');
      return null;
    }
  }

  /// Generate content with meter reading data
  Map<String, String> _generateContentWithMeterData(Map<String, dynamic> data) {
    final daysSince = (data['daysSinceLastReading'] as double).round();
    final lastValue = data['lastReadingValue'] as double;

    String title = 'Meter Reading Reminder';
    String message;

    if (daysSince == 0) {
      message =
          'Time for your meter reading! Your last reading was today (£${lastValue.toStringAsFixed(2)}).';
    } else if (daysSince == 1) {
      message =
          'Time for your meter reading! It\'s been 1 day since your last reading (£${lastValue.toStringAsFixed(2)}).';
    } else if (daysSince <= 7) {
      message =
          'Time for your meter reading! It\'s been $daysSince days since your last reading (£${lastValue.toStringAsFixed(2)}).';
    } else if (daysSince <= 14) {
      final weeks = (daysSince / 7).floor();
      final extraDays = daysSince % 7;
      String timeDesc = '$weeks week${weeks > 1 ? 's' : ''}';
      if (extraDays > 0) {
        timeDesc += ' and $extraDays day${extraDays > 1 ? 's' : ''}';
      }
      message =
          'Time for your meter reading! It\'s been $timeDesc since your last reading.';
    } else {
      message =
          'Time for your meter reading! It\'s been $daysSince days since your last reading.';
    }

    return {
      'title': title,
      'message': message,
    };
  }

  /// Generate fallback content when meter data unavailable
  Map<String, String> _generateFallbackContent() {
    final messages = [
      'Time for your meter reading! Don\'t forget to check your electricity meter.',
      'Meter reading reminder: It\'s time to record your current meter reading.',
      'Don\'t forget your meter reading - stay on top of your electricity usage!',
      'Time to check your meter! Regular readings help you track your usage.',
      'Meter reading time! Take a moment to record your current reading.',
    ];

    // Use a simple rotation based on current time to vary messages
    final messageIndex = DateTime.now().hour % messages.length;

    return {
      'title': 'Meter Reading Reminder',
      'message': messages[messageIndex],
    };
  }

  /// Generate content for specific reminder context (e.g., weekly, daily)
  Future<Map<String, String>> generateContextualContent(
      String frequency) async {
    final baseContent = await generateReminderContent();

    // Add frequency context to title if desired
    String contextualTitle = baseContent['title']!;

    switch (frequency.toLowerCase()) {
      case 'daily':
        contextualTitle = 'Daily Meter Reading';
        break;
      case 'weekly':
        contextualTitle = 'Weekly Meter Reading';
        break;
      case 'bi-weekly':
        contextualTitle = 'Bi-weekly Meter Reading';
        break;
      case 'monthly':
        contextualTitle = 'Monthly Meter Reading';
        break;
    }

    return {
      'title': contextualTitle,
      'message': baseContent['message']!,
    };
  }
}
