// File: lib/core/utils/date_time_utils.dart
import 'package:intl/intl.dart';
import 'date_formatter.dart';

/// Utility class for date and time operations
class DateTimeUtils {
  // Private constructor to prevent instantiation
  DateTimeUtils._();

  /// Formats a date according to the specified format
  static String formatDate(DateTime date, String format) {
    return DateFormat(format).format(date);
  }

  /// Formats a date using the default format (DD-MM-YYYY)
  static String formatDateDefault(DateTime date) {
    return DateFormat('dd-MM-yyyy').format(date);
  }

  /// Formats a date with time (DD-MM-YYYY HH:MM)
  static String formatDateWithTime(DateTime date) {
    return DateFormat('dd-MM-yyyy HH:mm').format(date);
  }

  /// Formats a date with day name (DDD, DD-MM-YYYY)
  static String formatDateWithDayName(DateTime date) {
    return DateFormat('EEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with full day name (DDDD, DD-MM-YYYY)
  static String formatDateWithFullDayName(DateTime date) {
    return DateFormat('EEEE, dd-MM-yyyy').format(date);
  }

  /// Formats a date with month name (DD MMM YYYY)
  static String formatDateWithMonthName(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }

  /// Formats a date with full month name (DD MMMM YYYY)
  static String formatDateWithFullMonthName(DateTime date) {
    return DateFormat('dd MMMM yyyy').format(date);
  }

  /// Formats time (HH:MM)
  static String formatTime(DateTime date) {
    return DateFormat('HH:mm').format(date);
  }

  /// Formats a date in a relative way (Today, Yesterday, etc.)
  static String formatDateRelative(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return formatDateDefault(date);
    }
  }

  /// Formats a date in a relative way with time (Today at HH:MM, etc.)
  static String formatDateRelativeWithTime(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    final time = DateFormat('HH:mm').format(date);

    if (dateOnly == today) {
      return 'Today at $time';
    } else if (dateOnly == yesterday) {
      return 'Yesterday at $time';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow at $time';
    } else {
      return '${formatDateDefault(date)} at $time';
    }
  }

  /// Calculates the difference in days between two dates
  static int daysBetween(DateTime from, DateTime to) {
    final fromDate = DateTime(from.year, from.month, from.day);
    final toDate = DateTime(to.year, to.month, to.day);
    return toDate.difference(fromDate).inDays;
  }

  /// Calculates the difference in days between two dates with decimal precision
  ///
  /// This method calculates the exact number of days (including fractional days)
  /// between two dates by using seconds-based precision.
  static double calculateDaysWithPrecision(DateTime from, DateTime to) {
    final seconds = to.difference(from).inSeconds;
    return seconds /
        (24 * 60 * 60); // Convert seconds to days with decimal precision
  }

  /// Calculates the time fraction of a day between two dates
  ///
  /// Returns a value between 0.0 and 1.0+ representing the fraction of a day
  /// Used for cost calculations with recent averages
  static double calculateTimeFractionOfDay(DateTime from, DateTime to) {
    return calculateDaysWithPrecision(from, to);
  }

  /// Checks if a date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// Checks if a date is yesterday
  static bool isYesterday(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// Checks if a date is tomorrow
  static bool isTomorrow(DateTime date) {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    return date.year == tomorrow.year &&
        date.month == tomorrow.month &&
        date.day == tomorrow.day;
  }

  /// Checks if a date is in the past
  static bool isPast(DateTime date) {
    return date.isBefore(DateTime.now());
  }

  /// Checks if a date is in the future
  static bool isFuture(DateTime date) {
    return date.isAfter(DateTime.now());
  }

  /// Gets the start of the day for a date
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Gets the end of the day for a date
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Gets the start of the week for a date (Monday)
  static DateTime startOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(date.year, date.month, date.day - (day - 1));
  }

  /// Gets the end of the week for a date (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final day = date.weekday;
    return DateTime(
        date.year, date.month, date.day + (7 - day), 23, 59, 59, 999);
  }

  /// Gets the start of the month for a date
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Gets the end of the month for a date
  static DateTime endOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0, 23, 59, 59, 999);
  }

  /// Gets the start of the year for a date
  static DateTime startOfYear(DateTime date) {
    return DateTime(date.year, 1, 1);
  }

  /// Gets the end of the year for a date
  static DateTime endOfYear(DateTime date) {
    return DateTime(date.year, 12, 31, 23, 59, 59, 999);
  }

  /// Format time to threshold (3-row format: Title, Days, Date)
  static String formatTimeToThreshold(double? days, {bool isExceeded = false}) {
    if (days == null) return 'Not enough data';
    if (isExceeded) return 'Exceeded';

    if (days < 1.0) {
      final targetDate =
          DateTime.now().add(Duration(hours: (days * 24).round()));
      if (isToday(targetDate)) return 'Today';
      if (isTomorrow(targetDate)) return 'Tomorrow';
      return DateFormatter.formatDateForDashboard(targetDate);
    } else {
      return '${days.toStringAsFixed(1)} days';
    }
  }

  /// Format time to meter zero (3-row format: Title, Days, Date)
  static String formatTimeToMeterZero(double? days) {
    if (days == null) return 'Not enough data';

    if (days < 1.0) {
      final targetDate =
          DateTime.now().add(Duration(hours: (days * 24).round()));
      return DateFormatter.formatDateForDashboard(targetDate);
    } else {
      return '${days.toStringAsFixed(1)} days';
    }
  }

  /// Calculate target date from days value
  static DateTime? calculateTargetDate(double? days) {
    if (days == null || days < 0) return null;
    return DateTime.now().add(Duration(hours: (days * 24).round()));
  }

  /// Get days display text for threshold
  static String getThresholdDaysText(double? days, {bool isExceeded = false}) {
    if (days == null) return 'Not enough data';
    if (isExceeded) return 'Exceeded';

    if (days < 1.0) {
      final targetDate = calculateTargetDate(days);
      if (targetDate != null) {
        if (isToday(targetDate)) return 'Today';
        if (isTomorrow(targetDate)) return 'Tomorrow';
      }
      return '< 1 day';
    } else {
      return '${days.toStringAsFixed(1)} days';
    }
  }

  /// Get date display text for threshold
  static String getThresholdDateText(double? days, String dateFormat,
      {bool isExceeded = false}) {
    if (days == null) return '';

    final targetDate = calculateTargetDate(days);
    if (targetDate == null) return '';

    return _formatDateByPreference(targetDate, dateFormat);
  }

  /// Get days display text for meter zero
  static String getMeterZeroDaysText(double? days) {
    if (days == null) return 'Not enough data';

    if (days < 1.0) {
      return '< 1 day';
    } else {
      return '${days.toStringAsFixed(1)} days';
    }
  }

  /// Get date display text for meter zero
  static String getMeterZeroDateText(double? days, String dateFormat) {
    if (days == null) return '';

    final targetDate = calculateTargetDate(days);
    if (targetDate == null) return '';

    return _formatDateByPreference(targetDate, dateFormat);
  }

  /// Format date according to user preference (no time)
  static String _formatDateByPreference(DateTime date, String dateFormat) {
    switch (dateFormat) {
      case 'MM-dd-yyyy':
        return DateFormat('MM-dd-yyyy').format(date);
      case 'yyyy-MM-dd':
        return DateFormat('yyyy-MM-dd').format(date);
      case 'dd-MM-yyyy':
      default:
        return DateFormat('dd-MM-yyyy').format(date);
    }
  }
}
