import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'progress_provider.freezed.dart';

/// Progress state for operations like import/export
@freezed
class ProgressState with _$ProgressState {
  const factory ProgressState({
    @Default(false) bool isActive,
    @Default(0.0) double progress,
    @Default('') String statusMessage,
    @Default('') String operationType,
    @Default(false) bool canCancel,
    @Default(false) bool isCancelled,
    @Default(false) bool isCompleted,
    @Default(false) bool isSuccessful,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) = _ProgressState;

  const ProgressState._();

  /// Check if operation is in progress
  bool get isInProgress => isActive && !isCompleted && !isCancelled;

  /// Get progress percentage as integer
  int get progressPercentage => (progress * 100).round();

  /// Check if operation failed
  bool get hasFailed => isCompleted && !isSuccessful;
}

/// Progress notifier for managing operation progress
class ProgressNotifier extends StateNotifier<ProgressState> {
  ProgressNotifier() : super(const ProgressState());

  /// Start a new operation
  void startOperation({
    required String operationType,
    required String initialMessage,
    bool canCancel = false,
    Map<String, dynamic>? metadata,
  }) {
    state = ProgressState(
      isActive: true,
      progress: 0.0,
      statusMessage: initialMessage,
      operationType: operationType,
      canCancel: canCancel,
      metadata: metadata,
    );
  }

  /// Update progress
  void updateProgress({
    required double progress,
    required String statusMessage,
    Map<String, dynamic>? metadata,
  }) {
    if (state.isCancelled) return;
    
    state = state.copyWith(
      progress: progress.clamp(0.0, 1.0),
      statusMessage: statusMessage,
      metadata: metadata ?? state.metadata,
    );
  }

  /// Complete operation successfully
  void completeSuccess({
    required String successMessage,
    Map<String, dynamic>? metadata,
  }) {
    state = state.copyWith(
      isCompleted: true,
      isSuccessful: true,
      progress: 1.0,
      statusMessage: successMessage,
      metadata: metadata ?? state.metadata,
    );
  }

  /// Complete operation with failure
  void completeFailure({
    required String errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    state = state.copyWith(
      isCompleted: true,
      isSuccessful: false,
      errorMessage: errorMessage,
      statusMessage: 'Operation failed',
      metadata: metadata ?? state.metadata,
    );
  }

  /// Cancel operation
  void cancelOperation() {
    if (!state.canCancel) return;
    
    state = state.copyWith(
      isCancelled: true,
      isCompleted: true,
      isSuccessful: false,
      statusMessage: 'Operation cancelled',
    );
  }

  /// Reset progress state
  void reset() {
    state = const ProgressState();
  }
}

/// Export progress provider
final exportProgressProvider = StateNotifierProvider<ProgressNotifier, ProgressState>((ref) {
  return ProgressNotifier();
});

/// Import progress provider  
final importProgressProvider = StateNotifierProvider<ProgressNotifier, ProgressState>((ref) {
  return ProgressNotifier();
});

/// Generic progress provider factory
StateNotifierProvider<ProgressNotifier, ProgressState> createProgressProvider(String name) {
  return StateNotifierProvider<ProgressNotifier, ProgressState>((ref) {
    return ProgressNotifier();
  });
}

/// Progress step definition
class ProgressStep {
  final String name;
  final String description;
  final double weight;

  const ProgressStep({
    required this.name,
    required this.description,
    this.weight = 1.0,
  });
}

/// Progress calculator for multi-step operations
class ProgressCalculator {
  final List<ProgressStep> steps;
  final Map<String, double> _stepProgress = {};
  
  ProgressCalculator(this.steps);

  /// Update progress for a specific step
  void updateStep(String stepName, double progress) {
    _stepProgress[stepName] = progress.clamp(0.0, 1.0);
  }

  /// Calculate overall progress
  double calculateOverallProgress() {
    if (steps.isEmpty) return 0.0;
    
    double totalWeight = steps.fold(0.0, (sum, step) => sum + step.weight);
    double weightedProgress = 0.0;
    
    for (final step in steps) {
      final stepProgress = _stepProgress[step.name] ?? 0.0;
      weightedProgress += (stepProgress * step.weight);
    }
    
    return weightedProgress / totalWeight;
  }

  /// Get current step description
  String getCurrentStepDescription() {
    for (final step in steps) {
      final progress = _stepProgress[step.name] ?? 0.0;
      if (progress < 1.0) {
        return step.description;
      }
    }
    return steps.isNotEmpty ? steps.last.description : '';
  }

  /// Reset all step progress
  void reset() {
    _stepProgress.clear();
  }
}

/// Common progress steps for export operations
class ExportProgressSteps {
  static const List<ProgressStep> steps = [
    ProgressStep(
      name: 'prepare',
      description: 'Preparing data...',
      weight: 0.1,
    ),
    ProgressStep(
      name: 'generate',
      description: 'Generating CSV...',
      weight: 0.7,
    ),
    ProgressStep(
      name: 'save',
      description: 'Saving file...',
      weight: 0.15,
    ),
    ProgressStep(
      name: 'verify',
      description: 'Verifying file...',
      weight: 0.05,
    ),
  ];
}

/// Common progress steps for import operations
class ImportProgressSteps {
  static const List<ProgressStep> steps = [
    ProgressStep(
      name: 'parse',
      description: 'Parsing CSV file...',
      weight: 0.2,
    ),
    ProgressStep(
      name: 'validate',
      description: 'Validating entries...',
      weight: 0.2,
    ),
    ProgressStep(
      name: 'conflicts',
      description: 'Checking for conflicts...',
      weight: 0.1,
    ),
    ProgressStep(
      name: 'import',
      description: 'Importing entries...',
      weight: 0.5,
    ),
  ];
}
