// File: lib/core/utils/average_calculator.dart
import '../models/meter_entry.dart';
import 'date_time_utils.dart';

/// Utility class for calculating electricity usage averages
class AverageCalculator {
  /// Calculates the Short-Term Usage per Day between a meter reading and the previous meter reading.
  static double calculateShortAverage(List<MeterEntry> entries, int index) {
    if (index <= 0 ||
        index >= entries.length ||
        entries[index].amountToppedUp > 0) {
      return 0.0;
    }

    // Find previous meter reading
    int prevIndex = -1;
    for (int i = index - 1; i >= 0; i--) {
      if (entries[i].amountToppedUp == 0) {
        prevIndex = i;
        break;
      }
    }

    if (prevIndex < 0) {
      return 0.0;
    }

    // Calculate days between readings with precision
    final days = DateTimeUtils.calculateDaysWithPrecision(
        entries[prevIndex].timestamp, entries[index].timestamp);

    if (days <= 0) {
      return 0.0;
    }

    // Sum any top-ups between the two readings
    double sumTopUps = 0.0;
    for (int i = prevIndex + 1; i < index; i++) {
      sumTopUps += entries[i].amountToppedUp;
    }

    // Calculate usage: previous reading - current reading + top-ups (prepaid meter goes down)
    final usage =
        entries[prevIndex].reading - entries[index].reading + sumTopUps;

    // Return usage per day
    return usage > 0 ? usage / days : 0.0;
  }

  /// Calculates the Total Average Usage per Day across all meter readings.
  static double calculateTotalAverage(List<MeterEntry> entries) {
    if (entries.isEmpty) {
      return 0.0;
    }

    // Create a sorted copy of entries
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Filter to only include meter readings (not top-ups)
    final meterReadings =
        sortedEntries.where((e) => e.amountToppedUp == 0).toList();

    if (meterReadings.length < 2) {
      return 0.0;
    }

    // Get first and last meter readings
    final firstReading = meterReadings.first;
    final lastReading = meterReadings.last;

    // Calculate days between first and last readings with precision
    final days = DateTimeUtils.calculateDaysWithPrecision(
        firstReading.timestamp, lastReading.timestamp);

    if (days <= 0) {
      return 0.0;
    }

    // Sum all top-ups between first and last readings
    double sumTopUps = 0.0;
    for (final entry in sortedEntries) {
      if (entry.amountToppedUp > 0 &&
          entry.timestamp.isAfter(firstReading.timestamp) &&
          entry.timestamp.isBefore(lastReading.timestamp)) {
        sumTopUps += entry.amountToppedUp;
      }
    }

    // Calculate total usage: first reading - last reading + top-ups (prepaid meter goes down)
    final totalUsage = firstReading.reading - lastReading.reading + sumTopUps;

    // Return usage per day
    return totalUsage > 0 ? totalUsage / days : 0.0;
  }

  /// Calculates the Short-Term Usage per Day for all meter readings (excluding the oldest one).
  /// This method updates the shortAverageAfterTopUp field in each MeterEntry object.
  static List<MeterEntry> calculateShortAveragesAfterTopUps(
      List<MeterEntry> entries) {
    if (entries.length < 2) return entries;

    // Create a copy of the entries list to avoid modifying the original
    final updatedEntries = List<MeterEntry>.from(entries);

    // Sort entries by timestamp to ensure chronological order
    updatedEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Find all meter readings (not top-ups)
    final meterReadings =
        updatedEntries.where((e) => e.amountToppedUp == 0).toList();

    // Calculate short average for each meter reading (except the first one)
    for (int i = 1; i < meterReadings.length; i++) {
      final currentReading = meterReadings[i];
      final prevReading = meterReadings[i - 1];

      // Calculate days between readings with precision
      final days = DateTimeUtils.calculateDaysWithPrecision(
          prevReading.timestamp, currentReading.timestamp);

      if (days <= 0) continue;

      // Sum any top-ups between the two readings
      double sumTopUps = 0.0;
      for (final entry in updatedEntries) {
        if (entry.amountToppedUp > 0 &&
            entry.timestamp.isAfter(prevReading.timestamp) &&
            entry.timestamp.isBefore(currentReading.timestamp)) {
          sumTopUps += entry.amountToppedUp;
        }
      }

      // Calculate usage: previous reading - current reading + top-ups (prepaid meter goes down)
      final usage = prevReading.reading - currentReading.reading + sumTopUps;

      // Calculate short average
      final shortAverage = usage > 0 ? usage / days : 0.0;

      // Find the index of the current reading in the original list
      final index = updatedEntries.indexWhere((e) =>
          e.id == currentReading.id ||
          (e.timestamp == currentReading.timestamp &&
              e.reading == currentReading.reading));

      if (index >= 0) {
        // Update the entry with the calculated short average
        updatedEntries[index] = updatedEntries[index].copyWith(
          shortAverageAfterTopUp: shortAverage,
        );
      }
    }

    // Calculate total average for all entries
    final totalAverage = calculateTotalAverage(updatedEntries);

    // Update total average for all entries
    for (int i = 0; i < updatedEntries.length; i++) {
      if (updatedEntries[i].amountToppedUp == 0) {
        updatedEntries[i] = updatedEntries[i].copyWith(
          totalAverageUpToThisPoint: totalAverage,
        );
      }
    }

    return updatedEntries;
  }

  /// Gets the most recent total average from the entries
  static double getMostRecentTotalAverage(List<MeterEntry> entries) {
    return calculateTotalAverage(entries);
  }

  /// Calculates the estimated date to top up based on the current meter total and average usage.
  static DateTime? calculateDateToTopUp(
      double meterTotal, double alertThreshold, double averageUsage) {
    if (averageUsage <= 0) return null;

    final daysUntilThreshold = (meterTotal - alertThreshold) / averageUsage;
    if (daysUntilThreshold <= 0) return null;

    return DateTime.now().add(Duration(days: daysUntilThreshold.round()));
  }

  /// Calculate days until alert threshold is reached using projected balance
  static double? calculateDaysToAlertThreshold({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double alertThreshold,
    required double? recentAverageUsage,
    required double? totalAverageUsage,
    required int daysInAdvance,
  }) {
    // Determine effective average usage with fallback
    double? effectiveAverageUsage = recentAverageUsage;
    if (effectiveAverageUsage == null || effectiveAverageUsage <= 0) {
      effectiveAverageUsage = totalAverageUsage;
    }
    if (effectiveAverageUsage == null || effectiveAverageUsage <= 0) {
      return null; // No usable average data
    }

    // Get projected balance using existing logic
    final projectedBalance = calculateProjectedBalance(
      lastMeterReading: lastMeterReading,
      topUpsSinceLastReading: topUpsSinceLastReading,
      lastReadingDate: lastReadingDate,
      averageUsage: effectiveAverageUsage,
    );

    // Check if already exceeded threshold
    if (projectedBalance <= alertThreshold) {
      return -1; // Indicates already exceeded
    }

    // Calculate days to threshold
    final daysToThreshold =
        (projectedBalance - alertThreshold) / effectiveAverageUsage;

    // Subtract days in advance for earlier warning
    final adjustedDays = daysToThreshold - daysInAdvance;

    // Return -1 if adjustment results in exceeded state
    return adjustedDays <= 0 ? -1 : adjustedDays;
  }

  /// Calculates the projected current balance based on days since last reading and average usage.
  ///
  /// [lastMeterReading] The last recorded meter reading value
  /// [topUpsSinceLastReading] The sum of all top-ups since the last reading
  /// [lastReadingDate] The date of the last meter reading
  /// [averageUsage] The average daily usage
  ///
  /// Returns the projected current balance
  static double calculateProjectedBalance({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double averageUsage,
  }) {
    // If average usage is zero or negative, just return the last reading plus top-ups
    if (averageUsage <= 0) {
      return lastMeterReading + topUpsSinceLastReading;
    }

    // Calculate days since last reading with precision
    final daysSinceLastReading = DateTimeUtils.calculateDaysWithPrecision(
        lastReadingDate, DateTime.now());

    // If the reading was taken less than an hour ago, just return the last reading plus top-ups
    if (daysSinceLastReading < 0.042) {
      // 0.042 days = 1 hour
      return lastMeterReading + topUpsSinceLastReading;
    }

    // Calculate estimated usage since last reading
    final estimatedUsage = daysSinceLastReading * averageUsage;

    // Calculate projected balance
    final projectedBalance =
        lastMeterReading + topUpsSinceLastReading - estimatedUsage;

    // Ensure the projected balance is not negative
    return projectedBalance > 0 ? projectedBalance : 0.0;
  }

  /// Calculates days remaining based on current balance and usage, accounting for reading date.
  ///
  /// [lastMeterReading] The last recorded meter reading value
  /// [topUpsSinceLastReading] The sum of all top-ups since the last reading
  /// [lastReadingDate] The date of the last meter reading
  /// [averageUsage] The average daily usage
  ///
  /// Returns the number of days remaining, or null if calculation is not possible
  static double? calculateDaysRemaining({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double averageUsage,
  }) {
    // Return null if average usage is zero or negative
    if (averageUsage <= 0) {
      return null;
    }

    // Calculate the projected current balance
    final projectedBalance = calculateProjectedBalance(
      lastMeterReading: lastMeterReading,
      topUpsSinceLastReading: topUpsSinceLastReading,
      lastReadingDate: lastReadingDate,
      averageUsage: averageUsage,
    );

    // Calculate days remaining
    return projectedBalance / averageUsage;
  }

  /// Calculate days until meter reaches zero balance
  static double? calculateDaysToMeterZero({
    required double lastMeterReading,
    required double topUpsSinceLastReading,
    required DateTime lastReadingDate,
    required double? recentAverageUsage,
    required double? totalAverageUsage,
  }) {
    // Determine effective average usage with fallback
    double? effectiveAverageUsage = recentAverageUsage;
    if (effectiveAverageUsage == null || effectiveAverageUsage <= 0) {
      effectiveAverageUsage = totalAverageUsage;
    }
    if (effectiveAverageUsage == null || effectiveAverageUsage <= 0) {
      return null; // No usable average data
    }

    // Get projected balance using existing logic
    final projectedBalance = calculateProjectedBalance(
      lastMeterReading: lastMeterReading,
      topUpsSinceLastReading: topUpsSinceLastReading,
      lastReadingDate: lastReadingDate,
      averageUsage: effectiveAverageUsage,
    );

    // Check if already at zero or negative
    if (projectedBalance <= 0) {
      return -1; // Indicates already at or below zero
    }

    // Calculate days to zero
    final daysToZero = projectedBalance / effectiveAverageUsage;

    return daysToZero;
  }
}
