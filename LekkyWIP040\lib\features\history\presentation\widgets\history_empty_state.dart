import 'package:flutter/material.dart';
import '../../../../core/shared/enums/entry_enums.dart';

/// A widget that displays an empty state for the History screen
class HistoryEmptyState extends StatelessWidget {
  /// The current filter type
  final EntryFilterType filterType;

  /// Whether date filters are applied
  final bool hasDateFilter;

  /// Callback when the clear filters button is pressed
  final VoidCallback onClearFilters;

  /// Constructor
  const HistoryEmptyState({
    super.key,
    required this.filterType,
    required this.hasDateFilter,
    required this.onClearFilters,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Determine message based on filter type and date filter
    String title;
    String message;
    IconData icon;

    if (hasDateFilter || filterType != EntryFilterType.all) {
      // Filtered empty state
      title = 'No entries found';
      message = 'Try adjusting your filters to see more entries';
      icon = Icons.filter_list;
    } else {
      // No entries empty state
      title = 'No entries yet';
      message = 'Add your first meter reading or top-up to get started';
      icon = Icons.history;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            if (hasDateFilter || filterType != EntryFilterType.all)
              ElevatedButton.icon(
                onPressed: onClearFilters,
                icon: const Icon(Icons.filter_list_off),
                label: const Text('Clear Filters'),
              ),
          ],
        ),
      ),
    );
  }
}
