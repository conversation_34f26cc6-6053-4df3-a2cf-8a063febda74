import 'reminder_event.dart';
import 'reminder_error.dart';

/// Status of the reminder system
enum ReminderStatus {
  idle,
  scheduling,
  scheduled,
  fired,
  rescheduling,
  error,
  cancelled,
}

/// Immutable state for reminder management
class ReminderState {
  /// Current reminder status
  final ReminderStatus status;

  /// Whether reminders are enabled
  final bool enabled;

  /// Reminder frequency ('daily', 'weekly', 'bi-weekly', 'monthly')
  final String frequency;

  /// Reminder start date and time
  final DateTime? startDateTime;

  /// Next scheduled reminder date/time
  final DateTime? nextReminderDate;

  /// Current error state
  final ReminderError? error;

  /// Event history buffer (last 5 events)
  final List<ReminderEvent> eventHistory;

  /// Scheduling metadata
  final int attemptCount;
  final DateTime? lastSuccessfulSchedule;

  /// Scheduled reminder ID for cancellation
  final int? scheduledReminderId;

  const ReminderState({
    this.status = ReminderStatus.idle,
    this.enabled = false,
    this.frequency = 'weekly',
    this.startDateTime,
    this.nextReminderDate,
    this.error,
    this.eventHistory = const [],
    this.attemptCount = 0,
    this.lastSuccessfulSchedule,
    this.scheduledReminderId,
  });

  /// Initial state
  factory ReminderState.initial() => const ReminderState();

  /// Create a copy with updated fields
  ReminderState copyWith({
    ReminderStatus? status,
    bool? enabled,
    String? frequency,
    DateTime? startDateTime,
    DateTime? nextReminderDate,
    ReminderError? error,
    List<ReminderEvent>? eventHistory,
    int? attemptCount,
    DateTime? lastSuccessfulSchedule,
    int? scheduledReminderId,
  }) {
    return ReminderState(
      status: status ?? this.status,
      enabled: enabled ?? this.enabled,
      frequency: frequency ?? this.frequency,
      startDateTime: startDateTime ?? this.startDateTime,
      nextReminderDate: nextReminderDate ?? this.nextReminderDate,
      error: error ?? this.error,
      eventHistory: eventHistory ?? this.eventHistory,
      attemptCount: attemptCount ?? this.attemptCount,
      lastSuccessfulSchedule:
          lastSuccessfulSchedule ?? this.lastSuccessfulSchedule,
      scheduledReminderId: scheduledReminderId ?? this.scheduledReminderId,
    );
  }
}

/// Extension methods for ReminderState
extension ReminderStateExtension on ReminderState {
  /// Check if reminder is currently active
  bool get isActive => enabled && nextReminderDate != null;

  /// Check if reminder is in error state
  bool get hasError => error != null;

  /// Check if reminder is currently being processed
  bool get isProcessing =>
      status == ReminderStatus.scheduling ||
      status == ReminderStatus.rescheduling;

  /// Get the most recent event
  ReminderEvent? get latestEvent =>
      eventHistory.isNotEmpty ? eventHistory.last : null;

  /// Check if reminder should be rescheduled
  bool get shouldReschedule => status == ReminderStatus.fired && enabled;

  /// Get user-friendly status message
  String get statusMessage {
    switch (status) {
      case ReminderStatus.idle:
        return enabled ? 'Ready to schedule' : 'Disabled';
      case ReminderStatus.scheduling:
        return 'Scheduling reminder...';
      case ReminderStatus.scheduled:
        return nextReminderDate != null
            ? 'Scheduled for ${_formatDateTime(nextReminderDate!)}'
            : 'Scheduled';
      case ReminderStatus.fired:
        return 'Reminder fired';
      case ReminderStatus.rescheduling:
        return 'Rescheduling next reminder...';
      case ReminderStatus.error:
        return error?.userMessage ?? 'Error occurred';
      case ReminderStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Format DateTime for display
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays > 0) {
      return '${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes';
    } else {
      return 'soon';
    }
  }

  /// Add event to history with automatic pruning
  ReminderState addEvent(ReminderEvent event) {
    final newHistory = [...eventHistory, event];

    // Keep only last 5 events
    if (newHistory.length > 5) {
      newHistory.removeRange(0, newHistory.length - 5);
    }

    return copyWith(eventHistory: newHistory);
  }

  /// Clear error state
  ReminderState clearError() {
    return copyWith(error: null);
  }

  /// Reset to initial state while preserving settings
  ReminderState reset() {
    return copyWith(
      status: ReminderStatus.idle,
      nextReminderDate: null,
      error: null,
      attemptCount: 0,
      scheduledReminderId: null,
    );
  }
}
