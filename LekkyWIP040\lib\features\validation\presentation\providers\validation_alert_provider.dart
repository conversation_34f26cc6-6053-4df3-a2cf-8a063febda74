import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../domain/services/data_integrity_service.dart';
import '../../domain/models/validation_alert_state.dart';

/// Reactive validation alert provider using Riverpod best practices
final validationAlertProvider =
    AsyncNotifierProvider<ValidationAlertNotifier, ValidationAlertState>(
  ValidationAlertNotifier.new,
);

/// Validation alert notifier for reactive state management
class ValidationAlertNotifier extends AsyncNotifier<ValidationAlertState> {
  Timer? _batchTimer;

  @override
  Future<ValidationAlertState> build() async {
    return await _loadInitialState();
  }

  /// Load initial validation alert state from preferences
  Future<ValidationAlertState> _loadInitialState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final invalidRecordAlertsEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final enabled = invalidRecordAlertsEnabled;

      return ValidationAlertState(
        enabled: enabled,
      );
    } catch (e) {
      Logger.error('Error loading initial validation alert state: $e');
      return ValidationAlertState.initial().copyWith(
        error: ValidationAlertError.unknown(details: e.toString()),
        status: ValidationAlertStatus.error,
      );
    }
  }

  /// Update validation alert settings reactively
  Future<void> updateSettings({
    bool? enabled,
  }) async {
    final currentState = await future;

    if (enabled != null && enabled != currentState.enabled) {
      try {
        final newState = currentState.copyWith(enabled: enabled);

        // Add settings changed event
        final updatedState = newState.addEvent(
          ValidationAlertEvent.settingsChanged(changes: {'enabled': enabled}),
        );

        state = AsyncValue.data(updatedState);

        // Clear pending entries if disabled
        if (!enabled && currentState.isBatching) {
          await _clearBatch('Settings disabled');
        }

        Logger.info('Validation alert settings updated: enabled=$enabled');
      } catch (e) {
        Logger.error('Error updating validation alert settings: $e');
        final errorState = currentState.copyWith(
          status: ValidationAlertStatus.error,
          error: ValidationAlertError.unknown(details: e.toString()),
        );
        state = AsyncValue.data(errorState.addEvent(
          ValidationAlertEvent.error(errorMessage: 'Failed to update settings'),
        ));
      }
    }
  }

  /// Add invalid entry to batch for notification
  Future<void> addInvalidEntry(String entryDetails) async {
    final currentState = await future;

    if (!currentState.enabled) {
      Logger.info('Validation alerts disabled, skipping entry: $entryDetails');
      return;
    }

    try {
      // Add entry to pending batch
      final updatedState = currentState
          .addPendingEntry(entryDetails)
          .addEvent(ValidationAlertEvent.entryAdded(
            entryDetails: entryDetails,
            totalPending: currentState.pendingEntries.length + 1,
          ));

      state = AsyncValue.data(updatedState);

      // Cancel existing timer and start new one
      _batchTimer?.cancel();
      _batchTimer =
          Timer(Duration(milliseconds: currentState.batchTimeoutMs), () {
        _processBatch();
      });

      Logger.info(
          'Invalid entry added to batch: $entryDetails (${updatedState.pendingEntries.length} pending)');
    } catch (e) {
      Logger.error('Error adding invalid entry to batch: $e');
      final errorState = currentState.copyWith(
        status: ValidationAlertStatus.error,
        error: ValidationAlertError.batchProcessingError(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        ValidationAlertEvent.error(
            errorMessage: 'Failed to add entry to batch'),
      ));
    }
  }

  /// Process batched invalid entry notifications
  Future<void> _processBatch() async {
    final currentState = await future;

    if (currentState.pendingEntries.isEmpty) {
      Logger.info('No pending entries to process');
      return;
    }

    try {
      // Update status to processing
      state = AsyncValue.data(
          currentState.copyWith(status: ValidationAlertStatus.processing));

      final entryCount = currentState.pendingEntries.length;

      // Note: Notification creation is handled by ValidationNotificationService
      // to ensure single source of truth and proper batching

      // Update state after successful notification
      final processedState = currentState
          .clearPendingEntries()
          .copyWith(
            status: ValidationAlertStatus.fired,
            lastNotificationSent: DateTime.now(),
          )
          .addEvent(
              ValidationAlertEvent.notificationSent(entryCount: entryCount));

      state = AsyncValue.data(processedState);

      Logger.info(
          'Validation notification sent successfully for $entryCount entries');
    } catch (e) {
      Logger.error('Error processing validation batch: $e');
      final errorState = currentState.copyWith(
        status: ValidationAlertStatus.error,
        error: ValidationAlertError.notificationServiceUnavailable(),
      );
      state = AsyncValue.data(errorState.addEvent(
        ValidationAlertEvent.error(errorMessage: 'Failed to send notification'),
      ));
    }
  }

  /// Clear current batch
  Future<void> _clearBatch(String reason) async {
    final currentState = await future;

    if (currentState.pendingEntries.isEmpty) return;

    _batchTimer?.cancel();

    final clearedState = currentState
        .clearPendingEntries()
        .addEvent(ValidationAlertEvent.batchCleared(reason: reason));

    state = AsyncValue.data(clearedState);

    Logger.info('Validation batch cleared: $reason');
  }

  /// Trigger immediate validation check for entry
  Future<void> validateAndNotifyIfInvalid(dynamic entry) async {
    final currentState = await future;

    if (!currentState.enabled) return;

    try {
      // Use existing validation service to check entry
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      final issues = await dataIntegrityService.validateSingleEntry(entry);

      if (issues.isNotEmpty) {
        // Create entry details for notification
        final entryType = entry.runtimeType.toString();
        final entryDate =
            entry.date?.toString().split(' ')[0] ?? 'Unknown date';
        final entryValue = entry.value?.toString() ??
            entry.amount?.toString() ??
            'Unknown value';

        final details = '$entryType on $entryDate (£$entryValue)';

        // Add to batch
        await addInvalidEntry(details);
      }
    } catch (e) {
      Logger.error('Error validating entry: $e');
      final errorState = currentState.copyWith(
        status: ValidationAlertStatus.error,
        error: ValidationAlertError.unknown(details: e.toString()),
      );
      state = AsyncValue.data(errorState.addEvent(
        ValidationAlertEvent.error(errorMessage: 'Failed to validate entry'),
      ));
    }
  }

  /// Force process current batch (for testing or manual trigger)
  Future<void> processBatchNow() async {
    _batchTimer?.cancel();
    await _processBatch();
  }

  /// Clear error state
  Future<void> clearError() async {
    final currentState = await future;
    if (currentState.hasError) {
      state = AsyncValue.data(currentState.clearError());
    }
  }

  /// Get batch status for UI
  String getBatchStatus() {
    final currentState = state.value;
    if (currentState == null) return 'Unknown';

    if (!currentState.enabled) return 'Disabled';
    if (currentState.pendingEntries.isEmpty) return 'No pending entries';

    final timeLeft = currentState.batchStartTime != null
        ? currentState.batchTimeoutMs -
            DateTime.now()
                .difference(currentState.batchStartTime!)
                .inMilliseconds
        : 0;

    if (timeLeft > 0) {
      return '${currentState.pendingEntries.length} entries pending (${(timeLeft / 1000).ceil()}s)';
    } else {
      return '${currentState.pendingEntries.length} entries ready to process';
    }
  }
}
