import 'package:flutter/material.dart';
import '../../../../core/widgets/lekky_button.dart';

/// A widget that displays the get started button in the welcome screen
class GetStartedButton extends StatelessWidget {
  /// Callback when the button is pressed
  final VoidCallback onPressed;

  /// Constructor
  const GetStartedButton({
    super.key,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
        text: 'Get Started',
        type: LekkyButtonType.primary,
        size: LekkyButtonSize.fullWidth,
        onPressed: onPressed,
      ),
    );
  }
}
