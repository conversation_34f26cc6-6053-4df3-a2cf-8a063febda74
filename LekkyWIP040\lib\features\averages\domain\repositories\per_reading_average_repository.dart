import '../../../../core/models/per_reading_average.dart';

/// Repository interface for per-reading average operations
abstract class PerReadingAverageRepository {
  /// Get all per-reading averages ordered by reading date
  Future<List<PerReadingAverage>> getAllPerReadingAverages();

  /// Get per-reading average for a specific meter reading
  Future<PerReadingAverage?> getPerReadingAverageByMeterReadingId(
      int meterReadingId);

  /// Save or update a per-reading average
  Future<int> savePerReadingAverage(PerReadingAverage perReadingAverage);

  /// Delete per-reading average by meter reading ID
  Future<int> deletePerReadingAverageByMeterReadingId(int meterReadingId);

  /// Delete all per-reading averages
  Future<int> deleteAllPerReadingAverages();

  /// Get per-reading averages for chart data (date range)
  Future<List<PerReadingAverage>> getPerReadingAveragesForDateRange(
      DateTime startDate, DateTime endDate);

  /// Get recent per-reading averages with limit (for chart display)
  Future<List<PerReadingAverage>> getRecentPerReadingAverages(int limit);

  /// Get the latest per-reading average (most recent by reading date)
  Future<PerReadingAverage?> getLatestPerReadingAverage();
}
