import 'package:freezed_annotation/freezed_annotation.dart';
import '../shared/models/theme_mode.dart';

part 'settings_state.freezed.dart';
part 'settings_state.g.dart';

/// Immutable state model for all app settings
@freezed
class SettingsState with _$SettingsState {
  const factory SettingsState({
    /// Language setting
    @Default('English') String language,

    /// Currency code (e.g., 'GBP', 'USD')
    @Default('GBP') String currency,

    /// Currency symbol (e.g., '£', '$')
    @Default('£') String currencySymbol,

    /// Alert threshold amount
    @Default(5.0) double alertThreshold,

    /// Days in advance for notifications
    @Default(5) int daysInAdvance,

    /// Whether reminders are enabled
    @Default(false) bool remindersEnabled,

    /// Whether low balance alerts are enabled
    @Default(false) bool lowBalanceAlertsEnabled,

    /// Whether time to top-up alerts are enabled
    @Default(false) bool timeToTopUpAlertsEnabled,

    /// Whether invalid record alerts are enabled
    @Default(false) bool invalidRecordAlertsEnabled,

    /// Reminder frequency ('daily', 'weekly', 'bi-weekly', 'monthly')
    @Default('weekly') String reminderFrequency,

    /// Reminder start date and time
    DateTime? reminderStartDateTime,

    /// Date format string (e.g., 'DD-MM-YYYY')
    @Default('DD-MM-YYYY') String dateFormat,

    /// Whether to show time with date
    @Default(false) bool showTimeWithDate,

    /// App theme mode
    @Default(AppThemeMode.system) AppThemeMode themeMode,
  }) = _SettingsState;

  factory SettingsState.fromJson(Map<String, dynamic> json) =>
      _$SettingsStateFromJson(json);
}

/// Extension methods for SettingsState
extension SettingsStateExtension on SettingsState {
  /// Get formatted currency display
  String get formattedCurrency => '$currencySymbol$currency';

  /// Get formatted alert threshold
  String get formattedAlertThreshold =>
      '$currencySymbol${alertThreshold.toStringAsFixed(2)}';

  /// Get formatted days in advance
  String get formattedDaysInAdvance => '$daysInAdvance days';

  /// Check if all notifications are disabled
  bool get allNotificationsDisabled => !hasEnabledAlerts;

  /// Check if any specific alerts are enabled
  bool get hasEnabledAlerts =>
      lowBalanceAlertsEnabled ||
      timeToTopUpAlertsEnabled ||
      invalidRecordAlertsEnabled;

  /// Check if reminders are fully configured
  bool get remindersFullyConfigured =>
      remindersEnabled && reminderStartDateTime != null;

  /// Get display name for theme mode
  String get themeModeDisplayName {
    switch (themeMode) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  /// Get display name for reminder frequency
  String get reminderFrequencyDisplayName {
    switch (reminderFrequency) {
      case 'daily':
        return 'Daily';
      case 'weekly':
        return 'Weekly';
      case 'bi-weekly':
        return 'Bi-weekly';
      case 'monthly':
        return 'Monthly';
      default:
        return 'Weekly';
    }
  }

  /// Validate settings
  bool get isValid {
    return alertThreshold >= 1.0 &&
        alertThreshold <= 999.99 &&
        daysInAdvance >= 1 &&
        daysInAdvance <= 99 &&
        language.isNotEmpty &&
        currency.isNotEmpty &&
        currencySymbol.isNotEmpty &&
        dateFormat.isNotEmpty;
  }

  /// Get validation errors
  List<String> get validationErrors {
    final errors = <String>[];

    if (alertThreshold < 1.0 || alertThreshold > 999.99) {
      errors.add('Alert threshold must be between £1.00 and £999.99');
    }

    if (daysInAdvance < 1 || daysInAdvance > 99) {
      errors.add('Days in advance must be between 1 and 99');
    }

    if (language.isEmpty) {
      errors.add('Language cannot be empty');
    }

    if (currency.isEmpty) {
      errors.add('Currency cannot be empty');
    }

    if (currencySymbol.isEmpty) {
      errors.add('Currency symbol cannot be empty');
    }

    if (dateFormat.isEmpty) {
      errors.add('Date format cannot be empty');
    }

    return errors;
  }
}
