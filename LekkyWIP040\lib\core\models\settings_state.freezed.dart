// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SettingsState _$SettingsStateFromJson(Map<String, dynamic> json) {
  return _SettingsState.fromJson(json);
}

/// @nodoc
mixin _$SettingsState {
  /// Language setting
  String get language => throw _privateConstructorUsedError;

  /// Currency code (e.g., 'GBP', 'USD')
  String get currency => throw _privateConstructorUsedError;

  /// Currency symbol (e.g., '£', '$')
  String get currencySymbol => throw _privateConstructorUsedError;

  /// Alert threshold amount
  double get alertThreshold => throw _privateConstructorUsedError;

  /// Days in advance for notifications
  int get daysInAdvance => throw _privateConstructorUsedError;

  /// Whether reminders are enabled
  bool get remindersEnabled => throw _privateConstructorUsedError;

  /// Whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled => throw _privateConstructorUsedError;

  /// Whether time to top-up alerts are enabled
  bool get timeToTopUpAlertsEnabled => throw _privateConstructorUsedError;

  /// Whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled => throw _privateConstructorUsedError;

  /// Reminder frequency ('daily', 'weekly', 'bi-weekly', 'monthly')
  String get reminderFrequency => throw _privateConstructorUsedError;

  /// Reminder start date and time
  DateTime? get reminderStartDateTime => throw _privateConstructorUsedError;

  /// Date format string (e.g., 'DD-MM-YYYY')
  String get dateFormat => throw _privateConstructorUsedError;

  /// Whether to show time with date
  bool get showTimeWithDate => throw _privateConstructorUsedError;

  /// App theme mode
  AppThemeMode get themeMode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SettingsStateCopyWith<SettingsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsStateCopyWith<$Res> {
  factory $SettingsStateCopyWith(
          SettingsState value, $Res Function(SettingsState) then) =
      _$SettingsStateCopyWithImpl<$Res, SettingsState>;
  @useResult
  $Res call(
      {String language,
      String currency,
      String currencySymbol,
      double alertThreshold,
      int daysInAdvance,
      bool remindersEnabled,
      bool lowBalanceAlertsEnabled,
      bool timeToTopUpAlertsEnabled,
      bool invalidRecordAlertsEnabled,
      String reminderFrequency,
      DateTime? reminderStartDateTime,
      String dateFormat,
      bool showTimeWithDate,
      AppThemeMode themeMode});
}

/// @nodoc
class _$SettingsStateCopyWithImpl<$Res, $Val extends SettingsState>
    implements $SettingsStateCopyWith<$Res> {
  _$SettingsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? language = null,
    Object? currency = null,
    Object? currencySymbol = null,
    Object? alertThreshold = null,
    Object? daysInAdvance = null,
    Object? remindersEnabled = null,
    Object? lowBalanceAlertsEnabled = null,
    Object? timeToTopUpAlertsEnabled = null,
    Object? invalidRecordAlertsEnabled = null,
    Object? reminderFrequency = null,
    Object? reminderStartDateTime = freezed,
    Object? dateFormat = null,
    Object? showTimeWithDate = null,
    Object? themeMode = null,
  }) {
    return _then(_value.copyWith(
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      alertThreshold: null == alertThreshold
          ? _value.alertThreshold
          : alertThreshold // ignore: cast_nullable_to_non_nullable
              as double,
      daysInAdvance: null == daysInAdvance
          ? _value.daysInAdvance
          : daysInAdvance // ignore: cast_nullable_to_non_nullable
              as int,
      remindersEnabled: null == remindersEnabled
          ? _value.remindersEnabled
          : remindersEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lowBalanceAlertsEnabled: null == lowBalanceAlertsEnabled
          ? _value.lowBalanceAlertsEnabled
          : lowBalanceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToTopUpAlertsEnabled: null == timeToTopUpAlertsEnabled
          ? _value.timeToTopUpAlertsEnabled
          : timeToTopUpAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      invalidRecordAlertsEnabled: null == invalidRecordAlertsEnabled
          ? _value.invalidRecordAlertsEnabled
          : invalidRecordAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      reminderFrequency: null == reminderFrequency
          ? _value.reminderFrequency
          : reminderFrequency // ignore: cast_nullable_to_non_nullable
              as String,
      reminderStartDateTime: freezed == reminderStartDateTime
          ? _value.reminderStartDateTime
          : reminderStartDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      showTimeWithDate: null == showTimeWithDate
          ? _value.showTimeWithDate
          : showTimeWithDate // ignore: cast_nullable_to_non_nullable
              as bool,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as AppThemeMode,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingsStateImplCopyWith<$Res>
    implements $SettingsStateCopyWith<$Res> {
  factory _$$SettingsStateImplCopyWith(
          _$SettingsStateImpl value, $Res Function(_$SettingsStateImpl) then) =
      __$$SettingsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String language,
      String currency,
      String currencySymbol,
      double alertThreshold,
      int daysInAdvance,
      bool remindersEnabled,
      bool lowBalanceAlertsEnabled,
      bool timeToTopUpAlertsEnabled,
      bool invalidRecordAlertsEnabled,
      String reminderFrequency,
      DateTime? reminderStartDateTime,
      String dateFormat,
      bool showTimeWithDate,
      AppThemeMode themeMode});
}

/// @nodoc
class __$$SettingsStateImplCopyWithImpl<$Res>
    extends _$SettingsStateCopyWithImpl<$Res, _$SettingsStateImpl>
    implements _$$SettingsStateImplCopyWith<$Res> {
  __$$SettingsStateImplCopyWithImpl(
      _$SettingsStateImpl _value, $Res Function(_$SettingsStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? language = null,
    Object? currency = null,
    Object? currencySymbol = null,
    Object? alertThreshold = null,
    Object? daysInAdvance = null,
    Object? remindersEnabled = null,
    Object? lowBalanceAlertsEnabled = null,
    Object? timeToTopUpAlertsEnabled = null,
    Object? invalidRecordAlertsEnabled = null,
    Object? reminderFrequency = null,
    Object? reminderStartDateTime = freezed,
    Object? dateFormat = null,
    Object? showTimeWithDate = null,
    Object? themeMode = null,
  }) {
    return _then(_$SettingsStateImpl(
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      alertThreshold: null == alertThreshold
          ? _value.alertThreshold
          : alertThreshold // ignore: cast_nullable_to_non_nullable
              as double,
      daysInAdvance: null == daysInAdvance
          ? _value.daysInAdvance
          : daysInAdvance // ignore: cast_nullable_to_non_nullable
              as int,
      remindersEnabled: null == remindersEnabled
          ? _value.remindersEnabled
          : remindersEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      lowBalanceAlertsEnabled: null == lowBalanceAlertsEnabled
          ? _value.lowBalanceAlertsEnabled
          : lowBalanceAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      timeToTopUpAlertsEnabled: null == timeToTopUpAlertsEnabled
          ? _value.timeToTopUpAlertsEnabled
          : timeToTopUpAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      invalidRecordAlertsEnabled: null == invalidRecordAlertsEnabled
          ? _value.invalidRecordAlertsEnabled
          : invalidRecordAlertsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      reminderFrequency: null == reminderFrequency
          ? _value.reminderFrequency
          : reminderFrequency // ignore: cast_nullable_to_non_nullable
              as String,
      reminderStartDateTime: freezed == reminderStartDateTime
          ? _value.reminderStartDateTime
          : reminderStartDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateFormat: null == dateFormat
          ? _value.dateFormat
          : dateFormat // ignore: cast_nullable_to_non_nullable
              as String,
      showTimeWithDate: null == showTimeWithDate
          ? _value.showTimeWithDate
          : showTimeWithDate // ignore: cast_nullable_to_non_nullable
              as bool,
      themeMode: null == themeMode
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as AppThemeMode,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingsStateImpl implements _SettingsState {
  const _$SettingsStateImpl(
      {this.language = 'English',
      this.currency = 'GBP',
      this.currencySymbol = '£',
      this.alertThreshold = 5.0,
      this.daysInAdvance = 5,
      this.remindersEnabled = false,
      this.lowBalanceAlertsEnabled = false,
      this.timeToTopUpAlertsEnabled = false,
      this.invalidRecordAlertsEnabled = false,
      this.reminderFrequency = 'weekly',
      this.reminderStartDateTime,
      this.dateFormat = 'DD-MM-YYYY',
      this.showTimeWithDate = false,
      this.themeMode = AppThemeMode.system});

  factory _$SettingsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingsStateImplFromJson(json);

  /// Language setting
  @override
  @JsonKey()
  final String language;

  /// Currency code (e.g., 'GBP', 'USD')
  @override
  @JsonKey()
  final String currency;

  /// Currency symbol (e.g., '£', '$')
  @override
  @JsonKey()
  final String currencySymbol;

  /// Alert threshold amount
  @override
  @JsonKey()
  final double alertThreshold;

  /// Days in advance for notifications
  @override
  @JsonKey()
  final int daysInAdvance;

  /// Whether reminders are enabled
  @override
  @JsonKey()
  final bool remindersEnabled;

  /// Whether low balance alerts are enabled
  @override
  @JsonKey()
  final bool lowBalanceAlertsEnabled;

  /// Whether time to top-up alerts are enabled
  @override
  @JsonKey()
  final bool timeToTopUpAlertsEnabled;

  /// Whether invalid record alerts are enabled
  @override
  @JsonKey()
  final bool invalidRecordAlertsEnabled;

  /// Reminder frequency ('daily', 'weekly', 'bi-weekly', 'monthly')
  @override
  @JsonKey()
  final String reminderFrequency;

  /// Reminder start date and time
  @override
  final DateTime? reminderStartDateTime;

  /// Date format string (e.g., 'DD-MM-YYYY')
  @override
  @JsonKey()
  final String dateFormat;

  /// Whether to show time with date
  @override
  @JsonKey()
  final bool showTimeWithDate;

  /// App theme mode
  @override
  @JsonKey()
  final AppThemeMode themeMode;

  @override
  String toString() {
    return 'SettingsState(language: $language, currency: $currency, currencySymbol: $currencySymbol, alertThreshold: $alertThreshold, daysInAdvance: $daysInAdvance, remindersEnabled: $remindersEnabled, lowBalanceAlertsEnabled: $lowBalanceAlertsEnabled, timeToTopUpAlertsEnabled: $timeToTopUpAlertsEnabled, invalidRecordAlertsEnabled: $invalidRecordAlertsEnabled, reminderFrequency: $reminderFrequency, reminderStartDateTime: $reminderStartDateTime, dateFormat: $dateFormat, showTimeWithDate: $showTimeWithDate, themeMode: $themeMode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingsStateImpl &&
            (identical(other.language, language) ||
                other.language == language) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.currencySymbol, currencySymbol) ||
                other.currencySymbol == currencySymbol) &&
            (identical(other.alertThreshold, alertThreshold) ||
                other.alertThreshold == alertThreshold) &&
            (identical(other.daysInAdvance, daysInAdvance) ||
                other.daysInAdvance == daysInAdvance) &&
            (identical(other.remindersEnabled, remindersEnabled) ||
                other.remindersEnabled == remindersEnabled) &&
            (identical(
                    other.lowBalanceAlertsEnabled, lowBalanceAlertsEnabled) ||
                other.lowBalanceAlertsEnabled == lowBalanceAlertsEnabled) &&
            (identical(
                    other.timeToTopUpAlertsEnabled, timeToTopUpAlertsEnabled) ||
                other.timeToTopUpAlertsEnabled == timeToTopUpAlertsEnabled) &&
            (identical(other.invalidRecordAlertsEnabled,
                    invalidRecordAlertsEnabled) ||
                other.invalidRecordAlertsEnabled ==
                    invalidRecordAlertsEnabled) &&
            (identical(other.reminderFrequency, reminderFrequency) ||
                other.reminderFrequency == reminderFrequency) &&
            (identical(other.reminderStartDateTime, reminderStartDateTime) ||
                other.reminderStartDateTime == reminderStartDateTime) &&
            (identical(other.dateFormat, dateFormat) ||
                other.dateFormat == dateFormat) &&
            (identical(other.showTimeWithDate, showTimeWithDate) ||
                other.showTimeWithDate == showTimeWithDate) &&
            (identical(other.themeMode, themeMode) ||
                other.themeMode == themeMode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      language,
      currency,
      currencySymbol,
      alertThreshold,
      daysInAdvance,
      remindersEnabled,
      lowBalanceAlertsEnabled,
      timeToTopUpAlertsEnabled,
      invalidRecordAlertsEnabled,
      reminderFrequency,
      reminderStartDateTime,
      dateFormat,
      showTimeWithDate,
      themeMode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsStateImplCopyWith<_$SettingsStateImpl> get copyWith =>
      __$$SettingsStateImplCopyWithImpl<_$SettingsStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingsStateImplToJson(
      this,
    );
  }
}

abstract class _SettingsState implements SettingsState {
  const factory _SettingsState(
      {final String language,
      final String currency,
      final String currencySymbol,
      final double alertThreshold,
      final int daysInAdvance,
      final bool remindersEnabled,
      final bool lowBalanceAlertsEnabled,
      final bool timeToTopUpAlertsEnabled,
      final bool invalidRecordAlertsEnabled,
      final String reminderFrequency,
      final DateTime? reminderStartDateTime,
      final String dateFormat,
      final bool showTimeWithDate,
      final AppThemeMode themeMode}) = _$SettingsStateImpl;

  factory _SettingsState.fromJson(Map<String, dynamic> json) =
      _$SettingsStateImpl.fromJson;

  @override

  /// Language setting
  String get language;
  @override

  /// Currency code (e.g., 'GBP', 'USD')
  String get currency;
  @override

  /// Currency symbol (e.g., '£', '$')
  String get currencySymbol;
  @override

  /// Alert threshold amount
  double get alertThreshold;
  @override

  /// Days in advance for notifications
  int get daysInAdvance;
  @override

  /// Whether reminders are enabled
  bool get remindersEnabled;
  @override

  /// Whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled;
  @override

  /// Whether time to top-up alerts are enabled
  bool get timeToTopUpAlertsEnabled;
  @override

  /// Whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled;
  @override

  /// Reminder frequency ('daily', 'weekly', 'bi-weekly', 'monthly')
  String get reminderFrequency;
  @override

  /// Reminder start date and time
  DateTime? get reminderStartDateTime;
  @override

  /// Date format string (e.g., 'DD-MM-YYYY')
  String get dateFormat;
  @override

  /// Whether to show time with date
  bool get showTimeWithDate;
  @override

  /// App theme mode
  AppThemeMode get themeMode;
  @override
  @JsonKey(ignore: true)
  _$$SettingsStateImplCopyWith<_$SettingsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
