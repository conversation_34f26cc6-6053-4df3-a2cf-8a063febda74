import '../models/top_up.dart';

/// Repository interface for top-ups
abstract class TopUpRepository {
  /// Get all top-ups
  Future<List<TopUp>> getAllTopUps();
  
  /// Get top-ups with pagination
  Future<List<TopUp>> getTopUps({
    required int page,
    required int pageSize,
  });
  
  /// Get top-up by ID
  Future<TopUp?> getTopUpById(int id);
  
  /// Get top-ups by date range
  Future<List<TopUp>> getTopUpsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
  });
  
  /// Get latest top-up
  Future<TopUp?> getLatestTopUp();
  
  /// Add a new top-up
  Future<int> addTopUp(TopUp topUp);
  
  /// Update an existing top-up
  Future<int> updateTopUp(TopUp topUp);
  
  /// Delete a top-up
  Future<int> deleteTopUp(int id);
  
  /// Get the count of top-ups
  Future<int> getTopUpsCount();
  
  /// Get total amount of top-ups in a date range
  Future<double> getTotalTopUpAmount({
    required DateTime startDate,
    required DateTime endDate,
  });
}
