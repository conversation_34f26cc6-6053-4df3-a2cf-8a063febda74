// File: lib/core/utils/cost_interval.dart
import '../models/meter_entry.dart';
import 'date_time_utils.dart';

/// Represents a time interval for cost calculation
class CostInterval {
  /// Start date of the interval
  final DateTime startDate;

  /// End date of the interval
  final DateTime endDate;

  /// Usage rate per day during this interval
  final double usageRatePerDay;

  /// Constructor
  CostInterval({
    required this.startDate,
    required this.endDate,
    required this.usageRatePerDay,
  });

  /// Calculate the cost for this interval
  double calculateCost() {
    // Calculate days with precision
    final days = DateTimeUtils.calculateDaysWithPrecision(startDate, endDate);

    // Return cost (days * rate)
    return days * usageRatePerDay;
  }

  /// Calculate the cost for a portion of this interval
  double calculateCostForPortion(DateTime fromDate, DateTime toDate) {
    // Ensure the dates are within the interval
    final effectiveFromDate =
        fromDate.isBefore(startDate) ? startDate : fromDate;
    final effectiveToDate = toDate.isAfter(endDate) ? endDate : toDate;

    // If the effective dates are invalid, return 0
    if (effectiveFromDate.isAfter(effectiveToDate)) {
      return 0.0;
    }

    // Calculate days with precision
    final days = DateTimeUtils.calculateDaysWithPrecision(
        effectiveFromDate, effectiveToDate);

    // Return cost (days * rate)
    return days * usageRatePerDay;
  }

  /// Check if a date is within this interval
  bool containsDate(DateTime date) {
    return (date.isAfter(startDate) || date.isAtSameMomentAs(startDate)) &&
        (date.isBefore(endDate) || date.isAtSameMomentAs(endDate));
  }

  /// Check if this interval overlaps with another interval
  bool overlaps(CostInterval other) {
    return (startDate.isBefore(other.endDate) ||
            startDate.isAtSameMomentAs(other.endDate)) &&
        (endDate.isAfter(other.startDate) ||
            endDate.isAtSameMomentAs(other.startDate));
  }

  /// Create a list of cost intervals from meter entries
  static List<CostInterval> createIntervalsFromEntries(
      List<MeterEntry> entries) {
    if (entries.isEmpty) {
      return [];
    }

    // Create a sorted copy of entries
    final sortedEntries = List<MeterEntry>.from(entries)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Filter to only include meter readings (not top-ups)
    final meterReadings =
        sortedEntries.where((e) => e.amountToppedUp == 0).toList();

    if (meterReadings.length < 2) {
      return [];
    }

    final intervals = <CostInterval>[];

    // Create intervals between consecutive meter readings
    for (int i = 1; i < meterReadings.length; i++) {
      final prevReading = meterReadings[i - 1];
      final currReading = meterReadings[i];

      // Calculate days between readings with precision
      final days = DateTimeUtils.calculateDaysWithPrecision(
          prevReading.timestamp, currReading.timestamp);

      if (days <= 0) continue;

      // Sum any top-ups between the two readings
      double sumTopUps = 0.0;
      for (final entry in sortedEntries) {
        if (entry.amountToppedUp > 0 &&
            entry.timestamp.isAfter(prevReading.timestamp) &&
            entry.timestamp.isBefore(currReading.timestamp)) {
          sumTopUps += entry.amountToppedUp;
        }
      }

      // Calculate usage: previous reading + top-ups - current reading
      final usage = prevReading.value + sumTopUps - currReading.value;

      // Calculate usage rate per day
      final usageRatePerDay = usage > 0 ? usage / days : 0.0;

      // Create interval
      intervals.add(CostInterval(
        startDate: prevReading.timestamp,
        endDate: currReading.timestamp,
        usageRatePerDay: usageRatePerDay,
      ));
    }

    return intervals;
  }

  /// Calculate the cost for a date range using intervals
  static double calculateCostForDateRange(
      List<CostInterval> intervals, DateTime fromDate, DateTime toDate) {
    if (intervals.isEmpty || fromDate.isAfter(toDate)) {
      return 0.0;
    }

    double totalCost = 0.0;

    // Calculate cost for each interval that overlaps with the date range
    for (final interval in intervals) {
      if (interval.overlaps(CostInterval(
        startDate: fromDate,
        endDate: toDate,
        usageRatePerDay: 0.0,
      ))) {
        totalCost += interval.calculateCostForPortion(fromDate, toDate);
      }
    }

    return totalCost;
  }
}
