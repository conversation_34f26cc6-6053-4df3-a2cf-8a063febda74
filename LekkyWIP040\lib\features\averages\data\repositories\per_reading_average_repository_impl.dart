import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/constants/database_constants.dart';
import '../../../../core/models/per_reading_average.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/repositories/per_reading_average_repository.dart';

/// Implementation of the per-reading average repository
class PerReadingAverageRepositoryImpl implements PerReadingAverageRepository {
  final DatabaseHelper _databaseHelper;

  /// Constructor
  PerReadingAverageRepositoryImpl(this._databaseHelper);

  @override
  Future<List<PerReadingAverage>> getAllPerReadingAverages() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.perReadingAveragesTable,
        orderBy: 'reading_date ASC',
      );

      return maps.map((map) => PerReadingAverage.fromMap(map)).toList();
    } catch (e) {
      Logger.error('Failed to get all per-reading averages: $e');
      return [];
    }
  }

  @override
  Future<PerReadingAverage?> getPerReadingAverageByMeterReadingId(
      int meterReadingId) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.perReadingAveragesTable,
        where: 'meter_reading_id = ?',
        whereArgs: [meterReadingId],
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return PerReadingAverage.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get per-reading average by meter reading ID: $e');
      return null;
    }
  }

  @override
  Future<int> savePerReadingAverage(PerReadingAverage perReadingAverage) async {
    try {
      final db = await _databaseHelper.database;

      // Check if we have existing per-reading average for this meter reading
      final existing = await getPerReadingAverageByMeterReadingId(
          perReadingAverage.meterReadingId);

      if (existing != null) {
        // Update existing record
        return await db.update(
          DatabaseConstants.perReadingAveragesTable,
          perReadingAverage.toMap(),
          where: 'meter_reading_id = ?',
          whereArgs: [perReadingAverage.meterReadingId],
        );
      } else {
        // Insert new record
        return await db.insert(
          DatabaseConstants.perReadingAveragesTable,
          perReadingAverage.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    } catch (e) {
      Logger.error('Failed to save per-reading average: $e');
      return -1;
    }
  }

  @override
  Future<int> deletePerReadingAverageByMeterReadingId(
      int meterReadingId) async {
    try {
      final db = await _databaseHelper.database;
      return await db.delete(
        DatabaseConstants.perReadingAveragesTable,
        where: 'meter_reading_id = ?',
        whereArgs: [meterReadingId],
      );
    } catch (e) {
      Logger.error('Failed to delete per-reading average: $e');
      return -1;
    }
  }

  @override
  Future<int> deleteAllPerReadingAverages() async {
    try {
      final db = await _databaseHelper.database;
      return await db.delete(DatabaseConstants.perReadingAveragesTable);
    } catch (e) {
      Logger.error('Failed to delete all per-reading averages: $e');
      return -1;
    }
  }

  @override
  Future<List<PerReadingAverage>> getPerReadingAveragesForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.perReadingAveragesTable,
        where: 'reading_date BETWEEN ? AND ?',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
        orderBy: 'reading_date ASC',
      );

      return maps.map((map) => PerReadingAverage.fromMap(map)).toList();
    } catch (e) {
      Logger.error('Failed to get per-reading averages for date range: $e');
      return [];
    }
  }

  @override
  Future<List<PerReadingAverage>> getRecentPerReadingAverages(int limit) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.perReadingAveragesTable,
        orderBy: 'reading_date DESC',
        limit: limit,
      );

      // Convert to PerReadingAverage objects and reverse to maintain chronological order
      final averages =
          maps.map((map) => PerReadingAverage.fromMap(map)).toList();
      return averages.reversed.toList();
    } catch (e) {
      Logger.error('Failed to get recent per-reading averages: $e');
      return [];
    }
  }

  @override
  Future<PerReadingAverage?> getLatestPerReadingAverage() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseConstants.perReadingAveragesTable,
        orderBy: 'reading_date DESC',
        limit: 1,
      );

      if (maps.isNotEmpty) {
        Logger.info(
            'PerReadingAverageRepository: Found latest per-reading average for date: ${maps.first['reading_date']}');
        return PerReadingAverage.fromMap(maps.first);
      }

      Logger.info('PerReadingAverageRepository: No per-reading averages found');
      return null;
    } catch (e) {
      Logger.error('Failed to get latest per-reading average: $e');
      return null;
    }
  }
}
