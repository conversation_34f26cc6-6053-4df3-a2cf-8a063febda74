import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../constants/currency_constants.dart';
import '../models/localization_state.dart';

part 'riverpod_localization_provider.g.dart';

/// Provider for localization management using Riverpod
@riverpod
class RiverpodLocalization extends _$RiverpodLocalization {
  @override
  Future<LocalizationState> build() async {
    return await _loadLocalization();
  }

  /// Load localization from SharedPreferences
  Future<LocalizationState> _loadLocalization() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final language = prefs.getString(PreferenceKeys.language) ?? 'English';
      final languageCode = _getLanguageCode(language);

      return LocalizationState(
        language: language,
        languageCode: languageCode,
        isRTL: _isRTL(languageCode),
      );
    } catch (e) {
      // Return default localization on error
      return const LocalizationState();
    }
  }

  /// Convert language name to language code
  String _getLanguageCode(String language) {
    switch (language) {
      case 'English':
        return 'en';
      case 'Spanish':
        return 'es';
      case 'French':
        return 'fr';
      case 'German':
        return 'de';
      case 'Italian':
        return 'it';
      case 'Portuguese':
        return 'pt';
      case 'Russian':
        return 'ru';
      case 'Chinese':
        return 'zh';
      case 'Japanese':
        return 'ja';
      case 'Hindi':
        return 'hi';
      case 'Arabic':
        return 'ar';
      case 'Hebrew':
        return 'he';
      default:
        return 'en';
    }
  }

  /// Check if language is right-to-left
  bool _isRTL(String languageCode) {
    return ['ar', 'he', 'fa', 'ur'].contains(languageCode);
  }

  /// Update language
  Future<void> updateLanguage(String language) async {
    final currentState = await future;
    if (currentState.language == language) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(PreferenceKeys.language, language);

      final languageCode = _getLanguageCode(language);

      // Update state
      state = AsyncValue.data(LocalizationState(
        language: language,
        languageCode: languageCode,
        isRTL: _isRTL(languageCode),
      ));
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Update language by code
  Future<void> updateLanguageCode(String languageCode) async {
    final language = _getLanguageName(languageCode);
    await updateLanguage(language);
  }

  /// Convert language code to language name
  String _getLanguageName(String languageCode) {
    final language = RegionalConstants.getLanguage(languageCode);
    return language?.name ?? 'English';
  }

  /// Get supported languages
  List<String> get supportedLanguages =>
      RegionalConstants.languages.map((lang) => lang.name).toList();

  /// Get supported language codes
  List<String> get supportedLanguageCodes =>
      RegionalConstants.supportedLanguageCodes;
}
