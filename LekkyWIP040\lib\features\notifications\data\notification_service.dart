import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../../../core/utils/logger.dart';
import '../domain/models/notification.dart';
import '../domain/repositories/notification_repository.dart';

/// Service for handling local notifications
class NotificationService {
  /// Flutter local notifications plugin
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;

  /// Notification repository
  final NotificationRepository _notificationRepository;

  /// Constructor
  NotificationService(
    this._flutterLocalNotificationsPlugin,
    this._notificationRepository,
  );

  /// Initialize the notification service
  Future<bool> initialize() async {
    try {
      // Initialize settings for Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Initialize settings for iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      // Initialize settings for all platforms
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin with error handling
      bool initialized = false;
      try {
        initialized = await _flutterLocalNotificationsPlugin.initialize(
              initializationSettings,
              onDidReceiveNotificationResponse: _onNotificationTapped,
            ) ??
            false;
      } catch (e) {
        Logger.error('Failed to initialize notification plugin: $e');
        return false;
      }

      if (!initialized) {
        Logger.error('Notification plugin initialization returned false');
        return false;
      }

      // Create notification channels for Android
      await _createNotificationChannels();

      // Request permission with error handling
      try {
        await _requestPermissions();
      } catch (e) {
        Logger.error('Failed to request notification permissions: $e');
        // Continue anyway, permissions might still work
      }

      Logger.info('Notification service initialized successfully');
      return true;
    } catch (e) {
      Logger.error('Failed to initialize notification service: $e');
      return false;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    try {
      // High priority channel for critical alerts (low balance, meter zero)
      const AndroidNotificationChannel highPriorityChannel =
          AndroidNotificationChannel(
        'lekky_critical_alerts',
        'Critical Alerts',
        description: 'Critical alerts for low balance and meter zero warnings',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
      );

      // Medium priority channel for threshold alerts
      const AndroidNotificationChannel mediumPriorityChannel =
          AndroidNotificationChannel(
        'lekky_threshold_alerts',
        'Threshold Alerts',
        description: 'Alerts when approaching your set threshold',
        importance: Importance.defaultImportance,
        enableVibration: true,
        playSound: true,
      );

      // Normal priority channel for reminders
      const AndroidNotificationChannel reminderChannel =
          AndroidNotificationChannel(
        'lekky_reminders',
        'Meter Reading Reminders',
        description: 'Reminders to take meter readings',
        importance: Importance.defaultImportance,
        enableVibration: true,
        playSound: true,
      );

      // Create channels
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(highPriorityChannel);

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(mediumPriorityChannel);

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(reminderChannel);

      Logger.info('Notification channels created successfully');
    } catch (e) {
      Logger.error('Failed to create notification channels: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    try {
      // Android permissions are now requested during initialization
      // iOS permissions are requested during initialization in DarwinInitializationSettings
    } catch (e) {
      Logger.error('Failed to request notification permissions: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      // Extract notification ID from the payload
      final int? notificationId = int.tryParse(response.payload ?? '');

      if (notificationId != null) {
        // Mark the notification as read
        _notificationRepository.markAsRead(notificationId);

        // Check if this is a reminder notification and handle auto-rescheduling
        _handleReminderNotificationTap(notificationId);

        // TODO: Navigate to notification screen or relevant screen
      }
    } catch (e) {
      Logger.error('Failed to handle notification tap: $e');
    }
  }

  /// Handle reminder notification tap for auto-rescheduling
  void _handleReminderNotificationTap(int notificationId) {
    try {
      // Get notification details to check type
      _notificationRepository
          .getNotificationById(notificationId)
          .then((notification) {
        if (notification?.type == NotificationType.readingReminder) {
          Logger.info(
              'Reminder notification tapped, setting background flag for auto-rescheduling');

          // Import and use BackgroundReminderFlags
          // Note: This would need proper import, but for now we'll use a simple approach
          _setReminderFiredFlag(notificationId);
        }
      }).catchError((error) {
        Logger.error('Failed to check notification type: $error');
      });
    } catch (e) {
      Logger.error('Failed to handle reminder notification tap: $e');
    }
  }

  /// Set reminder fired flag for background processing
  void _setReminderFiredFlag(int notificationId) {
    try {
      // This is a simplified approach - in the full implementation,
      // we would import BackgroundReminderFlags and use it properly
      Logger.info(
          'Setting reminder fired flag for notification: $notificationId');
      // BackgroundReminderFlags.setReminderFired(reminderDate: DateTime.now());
    } catch (e) {
      Logger.error('Failed to set reminder fired flag: $e');
    }
  }

  /// Show a notification immediately
  Future<void> showNotification(AppNotification notification) async {
    try {
      // Save notification to database
      final int notificationId =
          await _notificationRepository.addNotification(notification);

      if (notificationId == -1) {
        Logger.error('Failed to save notification to database');
        return;
      }

      // Create notification details for Android with appropriate channel
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        _getChannelId(notification.type),
        _getChannelName(notification.type),
        channelDescription: _getChannelDescription(notification.type),
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: '@mipmap/ic_launcher',
      );

      // Create notification details for iOS
      const DarwinNotificationDetails iOSNotificationDetails =
          DarwinNotificationDetails();

      // Create notification details for all platforms
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      );

      // Show the notification with enhanced error handling
      try {
        await _flutterLocalNotificationsPlugin.show(
          notificationId,
          notification.title,
          notification.message,
          notificationDetails,
          payload: notificationId.toString(),
        );
        Logger.info(
            'Notification shown successfully: ${notification.title} (ID: $notificationId)');
      } catch (e) {
        Logger.error('Failed to show notification to system: $e');
        Logger.error(
            'Notification details - Title: ${notification.title}, Type: ${notification.type}, Channel: ${_getChannelId(notification.type)}');
        rethrow;
      }
    } catch (e) {
      Logger.error('Failed to show notification: $e');
    }
  }

  /// Schedule a notification for a future time
  Future<void> scheduleNotification(
    AppNotification notification,
    DateTime scheduledDate,
  ) async {
    try {
      // Save notification to database
      final int notificationId =
          await _notificationRepository.addNotification(notification);

      if (notificationId == -1) {
        Logger.error('Failed to save notification to database');
        return;
      }

      // Create notification details for Android with appropriate channel
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        _getChannelId(notification.type),
        _getChannelName(notification.type),
        channelDescription: _getChannelDescription(notification.type),
        importance: _getImportance(notification.type),
        priority: _getPriority(notification.type),
        icon: '@mipmap/ic_launcher',
      );

      // Create notification details for iOS
      const DarwinNotificationDetails iOSNotificationDetails =
          DarwinNotificationDetails();

      // Create notification details for all platforms
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      );

      // Schedule the notification with enhanced error handling
      try {
        await _flutterLocalNotificationsPlugin.zonedSchedule(
          notificationId,
          notification.title,
          notification.message,
          tz.TZDateTime.from(scheduledDate, tz.local),
          notificationDetails,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          payload: notificationId.toString(),
        );
        Logger.info(
            'Notification scheduled successfully for ${scheduledDate.toIso8601String()}: ${notification.title} (ID: $notificationId)');
      } catch (e) {
        Logger.error('Failed to schedule notification to system: $e');
        Logger.error(
            'Notification details - Title: ${notification.title}, Type: ${notification.type}, Channel: ${_getChannelId(notification.type)}');
        rethrow;
      }
    } catch (e) {
      Logger.error('Failed to schedule notification: $e');
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      await _notificationRepository.deleteNotification(id);
      Logger.info('Notification cancelled: $id');
    } catch (e) {
      Logger.error('Failed to cancel notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      await _notificationRepository.deleteAllNotifications();
      Logger.info('All notifications cancelled');
    } catch (e) {
      Logger.error('Failed to cancel all notifications: $e');
    }
  }

  /// Get the importance level for Android notifications
  Importance _getImportance(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Importance.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return Importance.defaultImportance;
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return Importance.low;
    }
  }

  /// Get the priority level for Android notifications
  Priority _getPriority(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return Priority.high;
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return Priority.defaultPriority;
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return Priority.low;
    }
  }

  /// Get the appropriate channel ID for notification type
  String _getChannelId(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'lekky_critical_alerts';
      case NotificationType.timeToTopUp:
        return 'lekky_threshold_alerts';
      case NotificationType.invalidRecord:
        return 'lekky_threshold_alerts';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'lekky_reminders';
    }
  }

  /// Get the appropriate channel name for notification type
  String _getChannelName(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'Critical Alerts';
      case NotificationType.timeToTopUp:
        return 'Threshold Alerts';
      case NotificationType.invalidRecord:
        return 'Threshold Alerts';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'Meter Reading Reminders';
    }
  }

  /// Get the appropriate channel description for notification type
  String _getChannelDescription(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return 'Critical alerts for low balance and meter zero warnings';
      case NotificationType.timeToTopUp:
        return 'Alerts when approaching your set threshold';
      case NotificationType.invalidRecord:
        return 'Alerts when approaching your set threshold';
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 'Reminders to take meter readings';
    }
  }
}
