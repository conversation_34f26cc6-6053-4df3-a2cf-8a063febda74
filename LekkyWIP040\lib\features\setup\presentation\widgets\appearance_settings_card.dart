import 'package:flutter/material.dart';
import '../../../../core/shared/models/theme_mode.dart';
import '../../../../core/shared/widgets/theme_selector.dart';
import 'setup_section_header.dart';

/// A widget for appearance settings in the setup screen
class AppearanceSettingsCard extends StatelessWidget {
  /// Current theme mode
  final AppThemeMode themeMode;
  
  /// Callback when theme mode changes
  final Function(AppThemeMode) onThemeModeChanged;
  
  /// Constructor
  const AppearanceSettingsCard({
    Key? key,
    required this.themeMode,
    required this.onThemeModeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Appearance',
              description: 'Customize the look and feel of the app.',
              icon: Icons.palette,
            ),
            
            // Theme Mode Subsection
            ThemeSelector(
              currentThemeMode: themeMode,
              onThemeModeChanged: onThemeModeChanged,
            ),
          ],
        ),
      ),
    );
  }
}
