import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/constants/currency_constants.dart';
import '../../../../features/setup/presentation/widgets/radio_option.dart';

/// Language settings screen
class LanguageScreen extends ConsumerWidget {
  /// Constructor
  const LanguageScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: ref.watch(settingsProvider).when(
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(child: Text('Error: $error')),
            data: (settings) {
              return Column(
                children: [
                  // Banner with back arrow
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: AppBanner(
                      message: '← Language',
                      gradientColors: AppColors.getSettingsMainCardGradient(
                          Theme.of(context).brightness == Brightness.dark),
                      textColor: AppColors.getAppBarTextColor('settings',
                          Theme.of(context).brightness == Brightness.dark),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Card(
                            margin: const EdgeInsets.all(8.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Language section header
                                  Row(
                                    children: [
                                      const Icon(Icons.language,
                                          color: Colors.blue),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Language',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Current: ${settings.language}',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodySmall
                                                    ?.color,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  const Text(
                                    'Select your preferred language for the app interface.',
                                    style: TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),

                                  // Language options
                                  Column(
                                    children:
                                        RegionalConstants.languages.map((lang) {
                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 4),
                                        child: RadioOption<String>(
                                          value: lang.name,
                                          groupValue: settings.language,
                                          onChanged: (value) => ref
                                              .read(settingsProvider.notifier)
                                              .updateLanguage(value),
                                          title:
                                              '${lang.flagEmoji} ${lang.name}',
                                          icon: Icons.language,
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
    );
  }
}
