// File: lib/core/utils/error_types.dart

/// Types of errors that can occur in the application
enum ErrorType {
  /// Database error
  database,

  /// Validation error
  validation,

  /// Network error
  network,

  /// Permission error
  permission,

  /// File I/O error
  fileIOError,

  /// User cancelled operation
  userCancelled,

  /// Resource not found
  notFound,

  /// Invalid data error
  invalidData,

  /// Unknown error
  unknown,
}

/// Severity levels for errors
enum ErrorSeverity {
  /// Low severity - informational, doesn't affect functionality
  low,

  /// Medium severity - affects some functionality but app can continue
  medium,

  /// High severity - critical error that affects core functionality
  high,
}
