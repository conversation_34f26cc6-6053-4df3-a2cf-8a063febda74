import 'package:flutter/material.dart';

/// Utility class for determining battery icons based on days remaining
class BatteryIconUtils {
  /// Get appropriate battery icon based on days remaining until meter zero
  /// 
  /// Uses granular 8-level mapping for better visual progression:
  /// - > 10 days: battery_full (completely full)
  /// - 7-10 days: battery_6_bar (almost full)
  /// - 5-7 days: battery_5_bar (high)
  /// - 3-5 days: battery_4_bar (medium-high)
  /// - 2-3 days: battery_3_bar (medium)
  /// - 1-2 days: battery_2_bar (low)
  /// - 0-1 days: battery_1_bar (very low)
  /// - <= 0 days: battery_alert (critical/empty with warning)
  static IconData getBatteryIconForDays(double? daysRemaining) {
    if (daysRemaining == null) {
      return Icons.battery_unknown;
    }

    if (daysRemaining <= 0) {
      return Icons.battery_alert; // Critical state with warning symbol
    } else if (daysRemaining <= 1) {
      return Icons.battery_1_bar; // Very low
    } else if (daysRemaining <= 2) {
      return Icons.battery_2_bar; // Low
    } else if (daysRemaining <= 3) {
      return Icons.battery_3_bar; // Medium
    } else if (daysRemaining <= 5) {
      return Icons.battery_4_bar; // Medium-high
    } else if (daysRemaining <= 7) {
      return Icons.battery_5_bar; // High
    } else if (daysRemaining <= 10) {
      return Icons.battery_6_bar; // Almost full
    } else {
      return Icons.battery_full; // Completely full
    }
  }
}
