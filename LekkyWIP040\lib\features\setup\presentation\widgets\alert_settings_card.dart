import 'package:flutter/material.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../../../../core/shared/widgets/integer_input_field.dart';
import 'setup_section_header.dart';
import 'info_notice.dart';

/// A widget for alert settings in the setup screen
class AlertSettingsCard extends StatelessWidget {
  /// Current alert threshold
  final double alertThreshold;

  /// Current days in advance
  final int daysInAdvance;

  /// Currency symbol
  final String currencySymbol;

  /// Callback when alert threshold changes
  final Function(double) onAlertThresholdChanged;

  /// Callback when days in advance changes
  final Function(int) onDaysInAdvanceChanged;

  /// Constructor
  const AlertSettingsCard({
    Key? key,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.currencySymbol,
    required this.onAlertThresholdChanged,
    required this.onDaysInAdvanceChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SetupSectionHeader(
              title: 'Alert Settings',
              description:
                  'Configure when you want to receive alerts about your meter balance.',
              icon: Icons.notifications,
            ),

            // Alert Threshold Subsection
            const Text(
              'Alert Threshold',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'You will be notified when your balance falls below this amount.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            CurrencyInputField(
              value: alertThreshold,
              onChanged: (value) =>
                  value != null ? onAlertThresholdChanged(value) : null,
              currencySymbol: currencySymbol,
              labelText: 'Alert Threshold',
              hintText: 'Enter amount',
              minValue: 1.00,
              maxValue: 999.99,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 8),

            const InfoNotice(
              message:
                  'Low balance alerts will be active after you enter your first meter reading.',
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 8),

            Text(
              'Tip: Set this to the amount you typically top up with to get reminders at the right time.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),

            const SizedBox(height: 24),

            // Days in Advance Subsection
            const Text(
              'Days in Advance',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'How many days in advance should we notify you about low balance?',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),

            IntegerInputField(
              value: daysInAdvance,
              onChanged: (value) =>
                  value != null ? onDaysInAdvanceChanged(value) : null,
              suffixText: 'days',
              labelText: 'Days in Advance',
              hintText: 'Enter days',
              minValue: 0,
              maxValue: 99,
              borderRadius: BorderRadius.circular(8),
            ),

            const SizedBox(height: 8),

            const InfoNotice(
              message:
                  'Days in advance alerts will be active after you enter at least two meter readings to calculate your average usage.',
              icon: Icons.info_outline,
            ),

            const SizedBox(height: 8),

            Text(
              'Tip: Consider your usage patterns when setting this value. If you use electricity quickly, choose fewer days.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
